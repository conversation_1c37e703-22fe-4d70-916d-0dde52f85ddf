<a name="readme-top"></a>

<div align="center">
  <img src="src/assets/LinkedIn Cover Main.jpg" alt="logo" width="100%"  height="auto" />
  <br/>

  <h3><b>iVoteNow</b></h3>

</div>

# 📗 Table of Contents

- [📖 About the Project](#about-project)
  - [🛠️ Built With](#built-with)
    - [Tech Stack](#tech-stack)
    - [Key Features](#key-features)
  - [🚀 Live Demo](#live-demo)
  - [💻 Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Setup](#setup)
  - [Install](#install)
  - [Usage](#usage)
- [👥 Authors](#authors)
- [🔭 Future Features](#future-features)
- [🤝 Contributing](#contributing)
- [⭐ Show your support](#support)
- [🙏 Acknowledgements](#acknowledgements)
- [📝 License](#license)

# 📖 iVoteNow(Front End) <a name="about-project"></a>

iVoteNow a cutting-edge platform designed and developed with a seamless blend of technology and user convenience. This innovative voting system is powered by React, Ruby on Rails, PostgreSQL, and integrates the secure Paystack payment gateway for effortless payment processing.

With this system, users have the privilege to participate in the selection of nominees by casting their votes in a simple and user-friendly manner. The process is straightforward: users can browse through the list of nominees and select their preferred candidate with just a click. What sets this platform apart is its dual-payment method, allowing users to pay for their votes with either Mobile Money or Credit Card, ensuring flexibility and inclusively for all participants.

[Explore iVoteNow Backend](**************:CaptainBawa/iVoteNow-Server.git)

## 🛠️ Built With <a name="built-with"></a>

### Tech Stack <a name="tech-stack"></a>

<details>
  <summary>Client-Side</summary>
  <ul>
    <li><a href="https://react.dev/">React</a></li>
    <li><a href="https://vitejs.dev/">Vite</a></li>
    <li><a href="https://www.w3.org/Style/CSS/Overview.en.html">CSS</a></li>
  </ul>
</details>

<details>
  <summary>Database</summary>
  <ul>
    <li><a href="https://postgresql.org/">PostgreSQL</a></li>
  </ul>
</details>

<details>
  <summary>Server-Side</summary>
  <ul>
    <li><a href="https://rubyonrails.org/">Ruby on rails</a></li>
  </ul>
</details>

### Key Features <a name="key-features"></a>

**Find Nominee**: Easily discover your favorite nominee and cast your vote.

**User-Friendly Interface**: A clean and user-friendly interface ensures a seamless experience for voters.

**Pay With Mobile Money or Credit Card**: You can easily pay for your votes by using mobile money or credit cards.

**Nominees Profile**: Access comprehensive profiles of the nominees, including their brand, name, and photo.

**Secure and Private**: iVoteNow ensures the highest standards of data security and privacy.

## 🚀 Live Demo <a name="live-demo"></a>
Click [here](https://easevote.org/) to view the live demo

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## 💻 Getting Started <a name="getting-started"></a>

To get a local copy up and running, follow these steps.

### Prerequisites

In order to run this project you need:

```sh
 git version 2.38.x
```
```sh
 node.js version > 12.x
 ```
 ```sh
 IDE
 ```
```sh
 Browser (chrome, firefox, edge, safari, or brave)
```
### Setup

Clone this repository to your desired folder:

```sh
  cd my-folder
  <NAME_EMAIL>:CaptainBawa/iVoteNow-Client.git
```
### Install

Install this project with:

```sh
  cd iVoteNow-Client
  npm install
 ```
### Usage

To run the project, execute the following command:

```sh 
  npm run dev
  ```

## 👥 Authors <a name="authors"></a>

👤 **Collins Bawa**

- GitHub: [click here](https://github.com/CaptainBawa)
- LinkedIn: [click here](https://www.linkedin.com/in/captainbawa/)
- Twitter: [click here](https://twitter.com/BawaCollins)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## 🔭 Future Features <a name="future-features"></a>

- Add Sponsors Banner
- Add Different Awards

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## 🤝 Contributing <a name="contributing"></a>

Contributions, issues, and feature requests are welcome!

Feel free to check the [issues page](https://github.com/CaptainBawa/iVoteNow-Client/issues).

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## ⭐ Show your support <a name="support"></a>

Dear Viewers!

I'm excited to be working on this project and I would be grateful for your support! By supporting this project, you are not only helping me, but you are also contributing to something meaningful that can make a difference. Your support will give me the motivation and resources necessary to keep moving forward with this project and to ensure its success. So, whether it's through your kind words, by giving this project a star, your financial support, or simply by spreading the word, your support means everything to me. Thank you in advance for your support, and I can't wait to share with you the amazing things we will achieve together.

## 🙏 Acknowledgments <a name="acknowledgements"></a>

I want to express my gratitude to Easing Life Group of Companies for choosing me to be part of this meaningful project.

## 📝 License <a name="license"></a>

This project is [MIT](https://github.com/CaptainBawa/iVoteNow-Client/blob/development/LICENSE) licensed.

<p align="right">(<a href="#readme-top">back to top</a>)</p>