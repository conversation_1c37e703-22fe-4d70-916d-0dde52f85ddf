{"name": "ivotenow-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^1.9.7", "@vitejs/plugin-react": "^4.0.3", "axios": "^1.5.1", "chart.js": "^3.9.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-chartjs-2": "^4.3.1", "react-dom": "^18.2.0", "react-paystack": "^4.0.3", "react-redux": "^8.1.3", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.17.0", "react-toastify": "^9.1.3", "recharts": "^2.15.0", "vite": "^4.4.5"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/eslint-parser": "^7.22.15", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/preset-react": "^7.22.15", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "stylelint": "^13.13.1", "stylelint-config-standard": "^21.0.0", "stylelint-csstree-validator": "^1.9.0", "stylelint-scss": "^3.21.0"}}