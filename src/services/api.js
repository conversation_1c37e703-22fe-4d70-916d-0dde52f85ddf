export const API_BASE_URL = 'http://localhost:3000';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Helper function to get headers for requests
const getHeaders = (includeAuth = true) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const authHeaders = getAuthHeaders();
    return { ...headers, ...authHeaders };
  }
  
  return headers;
};

class ApiService {
  // Events
  async getEvents() {
    const response = await fetch(`${API_BASE_URL}/events`, {
      headers: getHeaders(false), // Public endpoint
    });
    if (!response.ok) {
      throw new Error('Failed to fetch events');
    }
    return response.json();
  }

  async getEvent(id) {
    const response = await fetch(`${API_BASE_URL}/events/${id}`, {
      headers: getHeaders(false), // Public endpoint
    });
    if (!response.ok) {
      throw new Error('Failed to fetch event');
    }
    return response.json();
  }

  async createEvent(eventData) {
    const response = await fetch(`${API_BASE_URL}/events`, {
      method: 'POST',
      headers: getHeaders(true), // Requires authentication
      body: JSON.stringify(eventData),
    });
    if (!response.ok) {
      throw new Error('Failed to create event');
    }
    return response.json();
  }

  async createEventWithImage(formData) {
    const response = await fetch(`${API_BASE_URL}/events`, {
      method: 'POST',
      headers: getAuthHeaders(), // Requires authentication
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to create event');
    }
    return response.json();
  }

  // Categories (nested under events)
  async getCategories(eventId) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories`, {
      headers: getHeaders(false), // Public endpoint
    });
    if (!response.ok) {
      throw new Error('Failed to fetch categories');
    }
    return response.json();
  }

  async createCategory(eventId, categoryData) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories`, {
      method: 'POST',
      headers: getHeaders(true), // Requires authentication
      body: JSON.stringify(categoryData),
    });
    if (!response.ok) {
      throw new Error('Failed to create category');
    }
    return response.json();
  }

  // Nominees (nested under events or under categories within events)
  async getNominees(eventId) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/nominees`, {
      headers: getHeaders(false), // Public endpoint
    });
    if (!response.ok) {
      throw new Error('Failed to fetch nominees');
    }
    return response.json();
  }

  async getNomineesByCategory(eventId, categoryId) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}/nominees`, {
      headers: getHeaders(false), // Public endpoint
    });
    if (!response.ok) {
      throw new Error('Failed to fetch nominees');
    }
    return response.json();
  }

  async createNominee(eventId, categoryId, nomineeData) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}/nominees`, {
      method: 'POST',
      headers: getHeaders(true), // Requires authentication
      body: JSON.stringify(nomineeData),
    });
    if (!response.ok) {
      throw new Error('Failed to create nominee');
    }
    return response.json();
  }

  async createNomineeWithImage(eventId, categoryId, formData) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}/nominees`, {
      method: 'POST',
      headers: getAuthHeaders(), // Requires authentication
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to create nominee');
    }
    return response.json();
  }

  async incrementVotes(nomineeId, incrementBy = 1) {
    const response = await fetch(`${API_BASE_URL}/nominees/${nomineeId}/increment_votes`, {
      method: 'PUT',
      headers: getHeaders(false), // Public endpoint - anyone can vote
      body: JSON.stringify({ increment_by: incrementBy }),
    });
    if (!response.ok) {
      throw new Error('Failed to increment votes');
    }
    return response.json();
  }

  // USSD
  async getUssdNomineeCodes(eventId) {
    const response = await fetch(`${API_BASE_URL}/ussd/nominee_codes/${eventId}`, {
      headers: getHeaders(false), // Public endpoint
    });
    if (!response.ok) {
      throw new Error('Failed to fetch USSD nominee codes');
    }
    return response.json();
  }

  // Payments
  async initiatePayment(paymentData) {
    const response = await fetch(`${API_BASE_URL}/payments/initiate`, {
      method: 'POST',
      headers: getHeaders(false), // Public endpoint - anyone can make payments
      body: JSON.stringify(paymentData),
    });
    if (!response.ok) {
      throw new Error('Failed to initiate payment');
    }
    return response.json();
  }

  // Update Event
  async updateEvent(eventId, eventData) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}`, {
      method: 'PUT',
      headers: getHeaders(true),
      body: JSON.stringify(eventData),
    });
    if (!response.ok) {
      throw new Error('Failed to update event');
    }
    return response.json();
  }

  // Delete Event
  async deleteEvent(eventId) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}`, {
      method: 'DELETE',
      headers: getHeaders(true),
    });
    if (!response.ok) {
      throw new Error('Failed to delete event');
    }
    return response.json();
  }

  // Update Category
  async updateCategory(eventId, categoryId, categoryData) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}`, {
      method: 'PUT',
      headers: getHeaders(true),
      body: JSON.stringify(categoryData),
    });
    if (!response.ok) {
      throw new Error('Failed to update category');
    }
    return response.json();
  }

  // Delete Category
  async deleteCategory(eventId, categoryId) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}`, {
      method: 'DELETE',
      headers: getHeaders(true),
    });
    if (!response.ok) {
      throw new Error('Failed to delete category');
    }
    return response.json();
  }

  // Update Nominee
  async updateNominee(eventId, categoryId, nomineeId, nomineeData) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}/nominees/${nomineeId}`, {
      method: 'PUT',
      headers: getHeaders(true),
      body: JSON.stringify(nomineeData),
    });
    if (!response.ok) {
      throw new Error('Failed to update nominee');
    }
    return response.json();
  }

  // Delete Nominee
  async deleteNominee(eventId, categoryId, nomineeId) {
    const response = await fetch(`${API_BASE_URL}/events/${eventId}/categories/${categoryId}/nominees/${nomineeId}`, {
      method: 'DELETE',
      headers: getHeaders(true),
    });
    if (!response.ok) {
      throw new Error('Failed to delete nominee');
    }
    return response.json();
  }

  // Admin Statistics
  async getAdminStatistics() {
    const response = await fetch(`${API_BASE_URL}/admin/statistics`, {
      headers: getAuthHeaders(),
    });
    if (!response.ok) {
      throw new Error('Failed to fetch admin statistics');
    }
    return response.json();
  }
}

export default new ApiService(); 