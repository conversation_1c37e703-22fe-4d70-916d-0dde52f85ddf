import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import App from './App';
import store from './reudx/store';
import { checkAuthStatus } from './reudx/slice/AuthSlice';

// Initialize authentication on app startup
const token = localStorage.getItem('authToken');
if (token) {
  store.dispatch(checkAuthStatus());
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>,
);
