*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: #212121;
  width: 100%;
  height: 100vh;
  font-family: 'Arial', sans-serif;
}

a {
  text-decoration: none;
  color: #000;
  font-size: 0.7rem;
  font-weight: 700;
}

.home-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  padding-bottom: 6rem;
}

.home-title-container {
  width: 90%;
  margin-top: 3%;
}

.home-title-container h2,
.home-title-container h3 {
  color: #fff;
}

.links {
  width: 45%;
  height: 25vh;
  border-radius: 10px;
  background: whitesmoke;
  border: none;
  padding: 2%;
  clip-path: polygon(30px 0%, 100% 0, 100% calc(100% - 30px), calc(100% - 30px) 100%, 0 100%, 0% 30px);
  border-top-right-radius: 20px;
  border-bottom-left-radius: 20px;
}

.icons-img {
  width: 40%;
  height: auto;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  padding-bottom: 6rem;
}

.card-container .card-container-inner {
  width: 46%;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0.3rem;
  backdrop-filter: blur(15px);
  box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.192),
    inset 0 0 5px rgba(255, 255, 255, 0.274), 0 5px 5px rgba(0, 0, 0, 0.164);
  transition: 0.5s;
}

.card-container-inner {
  backdrop-filter: none !important;
}

footer p {
  width: 95%;
  font-size: 0.9rem;
  font-weight: 700;
  text-align: center;
  line-height: 1.2;
  color: #fff;
}

.endvote h2,
.endvote p {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 800;
  line-height: 1.5;
  text-align: center;
}

.add-data h2,
.add-data button {
  color: #fff;
}

.nominees-card .nominees-title,
.card-container div h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  text-align: center;
}

.nominees-card .nominees-title {
  color: #fff;
  margin-bottom: 3%;
  margin-top: 5%;
}

.card-container div h2 {
  color: #ffffffc4;
  font-size: 1rem;
  line-height: 1.5;
}

.nav-inner img {
  width: 10%;
  height: 8vh;
}

.nominees-card div img {
  width: 100%;
  height: 22vh;
}

.form-div {
  display: flex;
  align-items: center;
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.form-div .close-btn {
  font-size: 1.5rem;
  font-weight: 900;
  position: absolute;
  left: 88%;
  top: 30%;
  border: none;
  color: #fff;
  background-color: transparent;
  z-index: 9000;
}

.vote-form {
  width: 90%;
  position: fixed;
  left: 5%;
  background-color: rgba(198, 198, 198, 0.34);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  border-bottom: 3px solid rgba(255, 255, 255, 0.440);
  border-left: 2px  rgba(255, 255, 255, 0.545) outset;
  box-shadow: -40px 50px 30px rgba(0, 0, 0, 0.280);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 5% 2%;
}

.vote-form label {
  font-weight: 600;
  margin-top: 10%;
  text-align: center;
  color: #ffffffc4;
}

.vote-form input,
.add-data input {
  color: #000;
  font-size: 1rem;
  padding: 3%;
  width: 100%;
  outline: none;
  border: 0;
  margin-top: 3%;
  background-color: #F3F3F3;
  border-radius: 10px;
}

.form-container {
  width: 100%;
}

.form-container .vote-btn,
.pay-btn {
  width: 100%;
  height: 6vh;
  background-color: #1e90ff;
  border: 0;
  border-radius: 0.5rem;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 0.625em 1em 0 rgba(30, 143, 255, 0.35);
}

.paystack-btn {
  height: 5vh;
  font-size: 1rem;
  font-weight: 600;
  border: 1px solid #111;
}

.search-bar {
  width: 100%;
  height: 5vh;
  outline: none;
  border: none;
  font-size: 1.2rem;
  border-radius: 10px;
  padding: 1%;
  margin-top: 3%;
}

footer {
  width: 100%;
  height: 5vh;
  position: fixed;
  bottom: 0;
  background-color: #1e90ff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 5%;
  box-shadow: rgba(0, 0, 0, 0.35) 0 -50px 36px -28px inset;
}

.thumbs-wrapper.axis-vertical {
  display: none !important;
}

.all-container {
  border: 1px solid #fff;
  background-color: #fff;
  margin: 2% 0;
}

.all-container div img {
  width: 10%;
  height: 10%;
}

.all-container div {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1%;
  border: 1px solid #000;
  margin-bottom: 1%;
}

.all-container div h3 {
  font-size: 0.6rem;
  font-weight: 900;
  line-height: 1.2;
}

.all-container button {
  height: 3vh;
  background-color: red;
  color: #fff;
  font-size: 0.5rem;
  font-weight: 700;
  border: none;
  padding: 0 1%;
}

.add-data button {
  width: 100%;
  background-color: green;
  border: none;
  font-size: 1.1rem;
  font-weight: 900;
  margin-top: 2%;
  cursor: pointer;
}

.winner {
  background-color: green;
  color: white;
}

.loading {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 5%;
}

.endvote {
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 900000;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5%;
  padding: 0 5%;
}

.marquee {
  display: inline-block;
  white-space: nowrap;
  position: relative;
  animation: marquee 35s linear infinite;
}

.marquee p {
  display: inline-block;
  text-align: left;
  font-size: 1.2rem;
  font-weight: 800;
  font-style: italic;
}

.results-page h1,
.no-res {
  color: whitesmoke;
}

.results-page {
  padding-bottom: 5rem;
  text-align: center;
}

.results-page select {
  margin-bottom: 20px;
  padding: 10px;
  width: 80%;
  border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  font-size: 1.2rem;
}

.chart-container {
  width: 95%;
}

.no-results {
  background-color: #fff;
  margin-top: -4%;
}

.no-results h2 {
  text-align: center;
  padding: 3%;
}

.no-results img {
  width: 100%;
  height: 100%;
}

@keyframes marquee {
  0% {
      transform: translateX(100%);
  }
  100% {
      transform: translateX(-100%);
  }
}

@media only screen and (min-width: 768px) {
  .nav-container .nav-inner {
    width: 30%;
    height: 9vh;
    left: 35%;
    top: 40%;
  }

  .search-bar {
    width: 35%;
    margin-top: 1%;
  }

  .home-title-container {
    width: 100%;
    margin-left: 3%;
  }

  .home-container {
    width: 90%;
    margin: 0 auto;
  }

  .home-title-container h2,
  .home-title-container h3 {
    font-size: 2rem;
  }

  .links {
    width: 15%;
    height: 23vh;
    padding: 1%;
  }

  .links h2 {
    font-size: 1.2rem;
  }

  footer p {
    font-size: 1.1rem;
    line-height: 1.5;
  }

  .nominees-card {
    width: 90%;
    margin: 0 auto;
  }

  .nominees-card .nominees-title {
    margin: 3% 0;
  }

  .card-container .card-container-inner {
    width: 13rem;
  }

  .endvote h2,
  .endvote p {
    font-size: 1.5rem;
  }

  .card-container div h2 {
    font-size: 1.2rem;
  }

  .vote-form {
    width: 40%;
    left: 30%;
  }

  .form-div .close-btn {
    left: 67%;
    top: 28%;
  }

  .add-data {
    width: 30%;
  }

  .add-data button {
    padding: 2% 0;
  }

  .over-main {
    width: 95%;
    margin: 0 auto;
    padding-bottom: 60px;
  }

  .all-main-container {
    display: flex;
    gap: 5%;
  }

  .all-container {
    width: 70%;
  }

  .all-container div {
    justify-content: flex-start;
    padding: 0 0.3%;
  }

  .all-container button {
    font-size: 0.9rem;
    cursor: pointer;
  }

  .all-container div h3 {
    font-size: 1rem;
    font-weight: 400;
    margin-right: auto;
  }
}
