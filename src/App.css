/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* CSS Variables for Design System */
:root {
  /* Colors */
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --background: #0f172a;
  --surface: #1e293b;
  --surface-light: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border: #334155;
  --border-light: #475569;

  /* Typography */
  --font-primary: 'Inter', 'Segoe UI', sans-serif;
  --font-accent: 'Poppins', sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: linear-gradient(135deg, var(--background) 0%, #1a202c 100%);
  min-height: 100vh;
  font-family: var(--font-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Global Link Styles */
a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

/* Header Styles */
.app-header {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-light);
}

/* Main Container */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem 0.7rem 4rem 0.7rem;
}

/* Home Page Styles */
.home-container {
  display: grid;
}

.home-title-container {
  text-align: center;
}

.home-title-container h1 {
  font-family: var(--font-accent);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.home-title-container h2 {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  font-weight: 400;
  margin-bottom: var(--spacing-lg);
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

/* Category Card */
.category-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-icon {
  width: 60px;
  height: 60px;
  margin-bottom: var(--spacing-md);
  border-radius: var(--radius-md);
  object-fit: cover;
}

.category-title {
  font-family: var(--font-accent);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.category-description {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* Nominees Section */
.nominees-section {
  margin-top: var(--spacing-2xl);
}

.nominees-title {
  font-family: var(--font-accent);
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.nominees-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

/* Nominee Cards Grid */
.nominees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  padding-bottom: var(--spacing-2xl);
}

.nominee-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.nominee-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.nominee-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.nominee-card:hover::before {
  transform: scaleX(1);
}

.nominee-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
}

.nominee-info {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.nominee-name {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.nominee-code {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 500;
}

footer p {
  width: 95%;
  font-size: 0.9rem;
  font-weight: 700;
  text-align: center;
  line-height: 1.2;
  color: #fff;
}

.endvote h2,
.endvote p {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 800;
  line-height: 1.5;
  text-align: center;
}

.add-data h2,
.add-data button {
  color: #fff;
}

.nominees-card .nominees-title,
.card-container div h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  text-align: center;
}

.nominees-card .nominees-title {
  color: #fff;
  margin-bottom: 3%;
  margin-top: 5%;
}

.card-container div h2 {
  color: #ffffffc4;
  font-size: 1rem;
  line-height: 1.5;
}

.nav-inner img {
  width: 10%;
  height: 8vh;
}

.nominees-card div img {
  width: 100%;
  height: 22vh;
}

.form-div {
  display: flex;
  align-items: center;
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.form-div .close-btn {
  font-size: 1.5rem;
  font-weight: 900;
  position: absolute;
  left: 88%;
  top: 30%;
  border: none;
  color: #fff;
  background-color: transparent;
  z-index: 9000;
}

.vote-form {
  width: 90%;
  position: fixed;
  left: 5%;
  background-color: rgba(198, 198, 198, 0.34);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  border-bottom: 3px solid rgba(255, 255, 255, 0.440);
  border-left: 2px  rgba(255, 255, 255, 0.545) outset;
  box-shadow: -40px 50px 30px rgba(0, 0, 0, 0.280);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 5% 2%;
}

.vote-form label {
  font-weight: 600;
  margin-top: 10%;
  text-align: center;
  color: #ffffffc4;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--surface-light);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover {
  background: var(--border-light);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--error), #dc2626);
  color: white;
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1.1rem;
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.85rem;
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-select {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: 1rem;
  outline: none;
  transition: var(--transition-normal);
}

.form-input:focus,
.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.paystack-btn {
  height: 5vh;
  font-size: 1rem;
  font-weight: 600;
  border: 1px solid #111;
}

/* Search Bar */
.search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto var(--spacing-xl);
}

.search-bar {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-left: 3rem;
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 1rem;
  font-family: var(--font-primary);
  outline: none;
  transition: var(--transition-normal);
}

.search-bar:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-bar::placeholder {
  color: var(--text-muted);
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.1rem;
}

footer {
  width: 100%;
  height: 5vh;
  position: fixed;
  bottom: 0;
  background-color: #1e90ff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 5%;
  box-shadow: rgba(0, 0, 0, 0.35) 0 -50px 36px -28px inset;
}

.thumbs-wrapper.axis-vertical {
  display: none !important;
}

.all-container {
  border: 1px solid #fff;
  background-color: #fff;
  margin: 2% 0;
}

.all-container div img {
  width: 10%;
  height: 10%;
}

.all-container div {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1%;
  border: 1px solid #000;
  margin-bottom: 1%;
}

.all-container div h3 {
  font-size: 0.6rem;
  font-weight: 900;
  line-height: 1.2;
}

.all-container button {
  height: 3vh;
  background-color: red;
  color: #fff;
  font-size: 0.5rem;
  font-weight: 700;
  border: none;
  padding: 0 1%;
}

.add-data button {
  width: 100%;
  background-color: green;
  border: none;
  font-size: 1.1rem;
  font-weight: 900;
  margin-top: 2%;
  cursor: pointer;
}

.winner {
  background-color: green;
  color: white;
}

.loading {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 5%;
}

.endvote {
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 900000;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5%;
  padding: 0 5%;
}

.marquee {
  display: inline-block;
  white-space: nowrap;
  position: relative;
  animation: marquee 35s linear infinite;
}

.marquee p {
  display: inline-block;
  text-align: left;
  font-size: 1.2rem;
  font-weight: 800;
  font-style: italic;
}

.results-page h1,
.no-res {
  color: whitesmoke;
}

.results-page {
  padding-bottom: 5rem;
  text-align: center;
}

.results-page select {
  margin-bottom: 20px;
  padding: 10px;
  width: 80%;
  border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  font-size: 1.2rem;
}

.chart-container {
  width: 95%;
}

.no-results {
  background-color: #fff;
  margin-top: -4%;
}

.no-results h2 {
  text-align: center;
  padding: 3%;
}

.no-results img {
  width: 100%;
  height: 100%;
}

@keyframes marquee {
  0% {
      transform: translateX(100%);
  }
  100% {
      transform: translateX(-100%);
  }
}

@media only screen and (min-width: 768px) {
  .nav-container .nav-inner {
    width: 30%;
    height: 9vh;
    left: 35%;
    top: 40%;
  }

  .search-bar {
    width: 35%;
    margin-top: 1%;
  }

  .home-title-container {
    width: 100%;
  }

  .main-container {
    padding: 0 0 5rem 0;
  }

  .home-container {
    width: 90%;
    margin: 0 auto;
  }

  .search-container {
    max-width: 1000px;
  }

  .search-icon {
    left: 35%;
    top: 60%;
  }

  .home-title-container h2,
  .home-title-container h3 {
    font-size: 2rem;
  }

  .links {
    width: 15%;
    height: 23vh;
    padding: 1%;
  }

  .links h2 {
    font-size: 1.2rem;
  }

  footer p {
    font-size: 1.1rem;
    line-height: 1.5;
  }

  .nominees-card {
    width: 90%;
    margin: 0 auto;
  }

  .nominees-card .nominees-title {
    margin: 3% 0;
  }

  .card-container .card-container-inner {
    width: 13rem;
  }

  .endvote h2,
  .endvote p {
    font-size: 1.5rem;
  }

  .card-container div h2 {
    font-size: 1.2rem;
  }

  .vote-form {
    width: 40%;
    left: 30%;
  }

  .form-div .close-btn {
    left: 67%;
    top: 28%;
  }

  .add-data {
    width: 30%;
  }

  .add-data button {
    padding: 2% 0;
  }

  .over-main {
    width: 95%;
    margin: 0 auto;
    padding-bottom: 60px;
  }

  .all-main-container {
    display: flex;
    gap: 5%;
  }

  .all-container {
    width: 70%;
  }

  .all-container div {
    justify-content: flex-start;
    padding: 0 0.3%;
  }

  .all-container button {
    font-size: 0.9rem;
    cursor: pointer;
  }

  .all-container div h3 {
    font-size: 1rem;
    font-weight: 400;
    margin-right: auto;
  }
}
