/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* CSS Variables for Design System */
:root {
  /* Colors */
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --background: #0f172a;
  --surface: #1e293b;
  --surface-light: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border: #334155;
  --border-light: #475569;

  /* Typography */
  --font-primary: 'Inter', 'Segoe UI', sans-serif;
  --font-accent: 'Poppins', sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: linear-gradient(135deg, var(--background) 0%, #1a202c 100%);
  min-height: 100vh;
  font-family: var(--font-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Global Link Styles */
a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

/* Header Styles */
.app-header {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-light);
}

.carousel .slide {
  margin-top: 4.5rem;
}

/* Main Container */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem 0.7rem 5rem 0.7rem;
}

/* Home Page Styles */
.home-container {
  display: grid;
}

.home-title-container {
  text-align: center;
}

.home-title-container h1 {
  font-family: var(--font-accent);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.home-title-container h2 {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  font-weight: 400;
  margin-bottom: var(--spacing-lg);
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

/* Category Card */
.category-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-icon-wrapper {
  position: relative;
  align-self: center;
  margin-bottom: var(--spacing-md);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  object-fit: cover;
  transition: var(--transition-normal);
}

.icon-overlay {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: var(--transition-normal);
}

.category-card:hover .icon-overlay {
  opacity: 1;
  transform: scale(1);
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

.category-title {
  font-family: var(--font-accent);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.category-description {
  font-size: 0.9rem;
  font-weight: 700;
  color: var(--text-muted);
  line-height: 1.5;
  text-align: center;
  flex: 1;
  margin-bottom: var(--spacing-md);
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border);
  margin-top: auto;
}

.vote-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary);
}

.arrow-icon {
  font-size: 1.2rem;
  color: var(--text-muted);
  transition: var(--transition-fast);
}

.category-card:hover .arrow-icon {
  color: var(--primary);
  transform: translateX(4px);
}

/* Logo Enhancement */
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-light);
}

/* Enhanced EventList Styles */
.events-title-container {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.title-section {
  margin-bottom: var(--spacing-xl);
}

.events-main-title {
  font-family: var(--font-accent);
  font-size: clamp(2.5rem, 6vw, 3.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.events-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

.events-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.search-container.enhanced {
  position: relative;
  max-width: 500px;
  width: 100%;
}

.search-container.enhanced .search-bar {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 50px;
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
  transition: var(--transition-normal);
}

.search-container.enhanced .search-bar:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-container.enhanced .search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.1rem;
}

.events-stats {
  display: flex;
  gap: var(--spacing-md);
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-icon {
  color: var(--primary);
  font-size: 1rem;
}

/* Enhanced Event Cards */
.event-card-wrapper {
  height: 100%;
}

.event-card.modern {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: var(--transition-normal);
  text-decoration: none;
  color: inherit;
  box-shadow: var(--shadow-md);
}

.event-card.modern:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.event-card-header {
  position: relative;
  overflow: hidden;
}

.event-image-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.event-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
  display: flex;
  align-items: center;
  justify-content: center;
}

.event-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.8), rgba(139, 92, 246, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

.event-card.modern:hover .event-overlay {
  opacity: 1;
}

.event-card.modern:hover .event-image {
  transform: scale(1.1);
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
}

.overlay-text {
  font-weight: 600;
  font-size: 0.9rem;
}

.event-status-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs) var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.8rem;
  font-weight: 600;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.active {
  background: #10b981;
}

.status-text {
  color: #000;
}

.event-content {
  flex: 1;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
}

.event-info {
  flex: 1;
}

.event-title {
  font-family: var(--font-accent);
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.event-description {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--background);
  border-radius: var(--radius-md);
}

.stat-item .stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
}

.stat-value {
  font-size: 0.95rem;
  color: var(--text-primary);
  font-weight: 600;
}

.event-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border);
  background: var(--surface-light);
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-text {
  font-weight: 600;
  color: var(--primary);
  font-size: 0.9rem;
}

.action-arrow {
  color: var(--text-muted);
  transition: var(--transition-fast);
}

.event-card.modern:hover .action-arrow {
  color: var(--primary);
  transform: translateX(4px);
}

/* Nominees Section */
.nominees-section {
  margin-top: 0.5rem;
}

.nominees-title {
  font-family: var(--font-accent);
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.nominees-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

/* Nominee Cards Grid */
.nominees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  padding-bottom: var(--spacing-2xl);
}

.nominee-card {
  padding: 0.5rem;
}

.nominee-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.nominee-card:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.nominee-card:hover::before {
  transform: scaleX(1);
}

.nominee-info {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.nominee-name {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.nominee-code {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 900;
}

footer p {
  width: 95%;
  font-size: 0.9rem;
  font-weight: 700;
  text-align: center;
  line-height: 1.2;
  color: #fff;
}

.endvote h2,
.endvote p {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 800;
  line-height: 1.5;
  text-align: center;
}

.add-data h2,
.add-data button {
  color: #fff;
}

.nominees-card .nominees-title,
.card-container div h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  text-align: center;
}

.nominees-card .nominees-title {
  color: #fff;
  margin-bottom: 3%;
  margin-top: 5%;
}

.card-container div h2 {
  color: #ffffffc4;
  font-size: 1rem;
  line-height: 1.5;
}

.nav-inner img {
  width: 10%;
  height: 8vh;
}

.nominees-card div img {
  width: 100%;
  height: 22vh;
}

.form-div {
  display: flex;
  align-items: center;
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.form-div .close-btn {
  font-size: 1.5rem;
  font-weight: 900;
  position: absolute;
  left: 88%;
  top: 30%;
  border: none;
  color: #fff;
  background-color: transparent;
  z-index: 9000;
}

.vote-form {
  width: 95%;
  position: fixed;
  background: #000000f7;
  border-radius: var(--radius-lg);
  border: 1px solid rgba(99, 102, 241, 0.2);
  box-shadow: -40px 50px 30px rgba(0, 0, 0, 0.280);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 5% 2%;
}

.vote-form label {
  font-weight: 600;
  text-align: center;
  color: #ffffffc4;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--surface-light);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover {
  background: var(--border-light);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--error), #dc2626);
  color: white;
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1.1rem;
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.85rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-select {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: 1rem;
  outline: none;
  transition: var(--transition-normal);
}

.form-input:focus,
.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* Results Page Styles */
.results-page {
  padding: var(--spacing-2xl) 0;
}

.results-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.results-title {
  font-family: var(--font-accent);
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.results-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 400;
}

.results-controls {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
  justify-content: center;
  align-items: end;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  min-width: 200px;
}

.control-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.chart-type-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.chart-section {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  margin-bottom: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
}

.chart-container {
  height: 400px;
  width: 100%;
}

.section-title {
  font-family: var(--font-accent);
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.winners-section {
  margin-bottom: var(--spacing-2xl);
}

.winners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.winner-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.winner-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.winner-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.trophy-icon {
  color: #ffd700;
  font-size: 1.2rem;
}

.category-name {
  font-family: var(--font-accent);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.winner-info {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.winner-image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-lg);
  object-fit: cover;
  border: 2px solid var(--primary);
}

.winner-details {
  flex: 1;
}

.winner-name {
  font-family: var(--font-accent);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.vote-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.winner-votes {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--success);
  font-weight: 600;
}

.total-votes {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.vote-percentage {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-block;
}

/* Detailed Results */
.detailed-results {
  margin-top: var(--spacing-2xl);
}

.category-results {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  margin-bottom: var(--spacing-xl);
}

.category-title {
  font-family: var(--font-accent);
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border);
}

.nominees-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.nominee-result {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.nominee-result:hover {
  transform: translateX(4px);
  border-color: var(--primary);
}

.nominee-result.winner {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 237, 78, 0.1));
  border-color: #ffd700;
}

.position-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--surface-light);
  border-radius: 50%;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.nominee-result.winner .position-badge {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: var(--background);
}

.nominee-result .nominee-image {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.nominee-result .nominee-info {
  flex: 1;
}

.nominee-result .nominee-name {
  font-family: var(--font-accent);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.nominee-result .nominee-votes {
  color: var(--text-secondary);
  font-weight: 500;
}

.winner-badge {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: var(--background);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: var(--spacing-sm);
}

/* Loading Animations */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-lg);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  font-weight: 500;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--error);
  font-size: 1.1rem;
  font-weight: 500;
}

/* Modern VoteForm Styles */
.vote-form-container {
  width: 100%;
}

.vote-trigger-btn {
  width: 100%;
  padding: 0.9rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-accent);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
}

.vote-trigger-btn:hover {
  box-shadow: var(--shadow-xl);
}

.vote-icon {
  font-size: 1.2rem;
}

.vote-form-modal {
  position: fixed;
  top: 5%;
  left: 0%;
  transform: translate(-50%, -50%);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  width: 100%;
  max-width: 551px;
  max-height: 90vh;
  overflow-y: auto;
}

.vote-form-modal::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: -1;
}

.vote-form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  border-bottom: 1px solid var(--border);
}

.vote-form-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-accent);
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.title-icon {
  color: var(--primary);
  font-size: 1.2rem;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.2rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.close-btn:hover {
  background: var(--surface-light);
  color: var(--text-primary);
}

.vote-form-content {
  padding: 0.5rem;
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-md);
}

.step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--surface-light);
  color: var(--text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition-normal);
}

.step.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
}

.step.completed {
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
}

.step-line {
  width: 60px;
  height: 2px;
  background: var(--border);
  transition: var(--transition-normal);
}

.step-line.completed {
  background: linear-gradient(90deg, var(--success), #059669);
}

.vote-info-card {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  text-align: center;
  margin-bottom: 1rem;
}

.info-icon {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: var(--spacing-sm);
}

.vote-info {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.vote-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.label-icon {
  margin-right: var(--spacing-xs);
  color: var(--primary);
}

.amount-input {
  font-weight: 600;
  text-align: center;
}

.votes-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 0.1rem;
  background: var(--background);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
}

.votes-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  font-family: var(--font-accent);
}

.votes-text {
  color: var(--text-secondary);
  font-weight: 500;
}

.btn-icon {
  margin-left: var(--spacing-sm);
}

.confirmation-card {
  text-align: center;
}

.confirmation-title {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.vote-summary {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
}

.summary-row:not(:last-child) {
  border-bottom: 1px solid var(--border);
}

.summary-row.total {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-primary);
  border-top: 2px solid var(--border);
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-sm);
}

.summary-label {
  color: var(--text-secondary);
}

.summary-value {
  color: var(--text-primary);
  font-weight: 600;
}

.confirmation-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-sm);
}

/* Advanced Loading Animations */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  gap: var(--spacing-lg);
}

.loading-message {
  color: var(--text-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
}

/* Awards Loading Animation */
.loading-awards {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.award-icon {
  font-size: 2rem;
  padding: var(--spacing-md);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.award-icon.trophy {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

.award-icon.medal {
  color: #c0c0c0;
  background: rgba(192, 192, 192, 0.1);
}

.award-icon.award {
  color: #cd7f32;
  background: rgba(205, 127, 50, 0.1);
}

/* Pulse Loading Animation */
.loading-pulse {
  position: relative;
  width: 80px;
  height: 80px;
}

.pulse-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  position: relative;
}

.pulse-circle::before,
.pulse-circle::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--primary);
  animation: pulse-ring 2s infinite;
}

.pulse-circle::after {
  animation-delay: 1s;
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Dots Loading Animation */
.loading-dots {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

/* Default Spinner */
.loading-spinner-default {
  width: 60px;
  height: 60px;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
}

/* Skeleton Loading */
.skeleton {
  background: linear-gradient(90deg, var(--surface) 25%, var(--surface-light) 50%, var(--surface) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1rem;
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.medium {
  width: 80%;
}

.skeleton-text.long {
  width: 100%;
}

.skeleton-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.skeleton-card {
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  background: var(--surface);
  border: 1px solid var(--border);
}

/* Page Transition Animations */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background: var(--background);
}

.admin-access-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--background);
}

.access-denied-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  max-width: 400px;
  box-shadow: var(--shadow-xl);
}

.access-denied-icon {
  font-size: 4rem;
  color: var(--error);
  margin-bottom: var(--spacing-lg);
}

.access-denied-card h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-family: var(--font-accent);
}

.access-denied-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.admin-header {
  background: var(--surface);
  border-bottom: 1px solid var(--border);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.admin-header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-icon {
  font-size: 1.5rem;
  color: var(--primary);
}

.admin-title h1 {
  font-family: var(--font-accent);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--text-secondary);
}

.admin-badge {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
}

.admin-nav {
  background: var(--surface-light);
  border-bottom: 1px solid var(--border);
  padding: 0;
}

.admin-nav-tabs {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  padding: 0 var(--spacing-lg);
}

.admin-nav-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  color: var(--text-muted);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border-bottom: 3px solid transparent;
}

.admin-nav-tab:hover {
  color: var(--text-primary);
  background: rgba(99, 102, 241, 0.1);
}

.admin-nav-tab.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.tab-icon {
  font-size: 1rem;
}

.admin-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-lg);
}

.admin-overview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.admin-stat-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  transition: var(--transition-normal);
}

.admin-stat-card.users {
  border-left: 4px solid #3b82f6;
}

.admin-stat-card.revenue {
  border-left: 4px solid #10b981;
}

.admin-stat-card.votes {
  border-left: 4px solid #f59e0b;
}

.admin-stat-card.events {
  border-left: 4px solid #8b5cf6;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.admin-stat-card.users .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.admin-stat-card.revenue .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.admin-stat-card.votes .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.admin-stat-card.events .stat-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content h3 {
  font-family: var(--font-accent);
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.stat-number {
  font-family: var(--font-accent);
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-charts-section {
  margin-top: var(--spacing-xl);
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
}

.chart-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.chart-card h3 {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.section-title {
  font-family: var(--font-accent);
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.admin-recent-events {
  margin-top: var(--spacing-2xl);
}

.events-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.event-overview-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.event-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.event-header h3 {
  font-family: var(--font-accent);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.event-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.event-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  color: var(--text-primary);
  font-weight: 600;
}

.event-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.admin-events {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.events-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.admin-event-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.event-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border);
}

.event-info h3 {
  font-family: var(--font-accent);
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.event-meta {
  display: flex;
  gap: var(--spacing-md);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.event-votes,
.event-revenue {
  font-weight: 500;
}

.event-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.categories-section h4,
.top-nominees-section h4 {
  font-family: var(--font-accent);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.categories-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.category-chip {
  background: var(--surface-light);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 120px;
}

.category-chip span:first-child {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.category-votes {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.nominees-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.nominee-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--surface-light);
  border-radius: var(--radius-sm);
}

.nominee-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.nominee-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nominee-name {
  font-weight: 600;
  color: var(--text-primary);
}

.nominee-votes {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.admin-users,
.admin-settings {
  text-align: center;
  padding: var(--spacing-2xl);
}

.users-stats {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
}

.user-stat-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  min-width: 200px;
}

.coming-soon {
  color: var(--text-muted);
  font-style: italic;
  font-size: 1.1rem;
}

.admin-error {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--error);
}

.admin-error h2 {
  margin-bottom: var(--spacing-md);
}

.admin-error p {
  margin-bottom: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .admin-nav-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }

  .admin-nav-tab {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .admin-stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .events-overview-grid {
    grid-template-columns: 1fr;
  }

  .event-details {
    grid-template-columns: 1fr;
  }

  .events-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .event-card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .event-actions {
    justify-content: center;
  }
}

/* Hero Section Styles */
.hero-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--background) 0%, var(--surface) 100%);
}

.hero-section {
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.hero-title {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin: 0;
}

.hero-title-main {
  font-family: var(--font-accent);
  font-size: clamp(3rem, 8vw, 4.5rem);
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
}

.hero-title-sub {
  font-family: var(--font-accent);
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  font-weight: 600;
  color: var(--text-secondary);
  line-height: 1.2;
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 500px;
  margin: 0;
}

.hero-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.feature-icon {
  color: var(--primary);
  font-size: 1.2rem;
  width: 20px;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-cta {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-lg);
}

.hero-cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
}

.hero-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.hero-cta:hover::before {
  left: 100%;
}

.btn-icon {
  font-size: 1rem;
}

.btn-arrow {
  font-size: 0.9rem;
  transition: var(--transition-fast);
}

.hero-cta:hover .btn-arrow {
  transform: translateX(4px);
}

.hero-secondary {
  padding: var(--spacing-md) var(--spacing-xl);
  background: transparent;
  border: 2px solid var(--border);
  color: var(--text-primary);
  text-decoration: none;
  transition: var(--transition-normal);
}

.hero-secondary:hover {
  background: var(--surface);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
}

/* Enhanced EventDetail Styles */
.event-hero {
  padding: 0.5rem;
  background: linear-gradient(135deg, var(--background), var(--surface));
}

.event-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.event-hero-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.event-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-md);
}

.breadcrumb-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
}

.breadcrumb-link:hover {
  color: var(--primary-dark);
}

.breadcrumb-separator {
  color: var(--text-muted);
}

.breadcrumb-current {
  color: var(--text-secondary);
  font-weight: 500;
}

.event-hero-title {
  font-family: var(--font-accent);
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.event-hero-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
}

.event-hero-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.hero-stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  transition: var(--transition-normal);
}

.hero-stat-card:hover {
  border-color: var(--primary);
  transform: translateX(4px);
}

.stat-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.stat-value.active {
  color: var(--success);
}

.event-admin-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.event-hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.event-image-container {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.event-detail-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  transition: var(--transition-normal);
}

.event-detail-image:hover {
  transform: scale(1.02);
}

.event-detail-image-placeholder {
  width: 100%;
  height: 300px;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-xl);
}

.image-overlay {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
}

.overlay-badge {
  background: rgba(16, 185, 129, 0.9);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

/* Enhanced Category Cards */
.category-card-wrapper {
  height: 100%;
}

.category-card.enhanced {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: var(--transition-normal);
  text-decoration: none;
  color: inherit;
  box-shadow: var(--shadow-md);
}

.category-card.enhanced:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.category-card-header {
  position: relative;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-icon-section {
  position: relative;
}

.category-icon-wrapper {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.category-main-icon {
  font-size: 1.8rem;
  color: white;
}

.category-overlay {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: var(--transition-normal);
}

.category-card.enhanced:hover .category-overlay {
  opacity: 1;
  transform: scale(1);
}

.overlay-icon {
  font-size: 0.8rem;
  color: var(--primary);
}

.category-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.badge-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.badge-text {
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
}

.category-content {
  flex: 1;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-content .category-title {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.category-content .category-description {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
  flex: 1;
}

.category-stats {
  display: flex;
  gap: var(--spacing-md);
}

.category-stat {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-muted);
  font-size: 0.9rem;
}

.category-stat .stat-icon {
  color: var(--primary);
}

.category-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border);
  background: var(--surface-light);
  border-radius: 0.5rem;
}

.category-card-footer .footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-card-footer .action-text {
  font-weight: 600;
  color: var(--primary);
  font-size: 0.9rem;
}

.category-card-footer .action-arrow {
  color: var(--text-muted);
  transition: var(--transition-fast);
}

.category-card.enhanced:hover .action-arrow {
  color: var(--primary);
  transform: translateX(4px);
}

/* Responsive EventDetail */
@media (max-width: 768px) {
  .event-hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .event-hero-text {
    order: 2;
  }

  .event-hero-image {
    order: 1;
  }

  .event-admin-actions {
    justify-content: center;
  }

  .event-breadcrumb {
    justify-content: center;
  }
}

/* Enhanced Nominee Cards */
.nominee-card.modern {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.nominee-card.modern:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.nominee-card-header {
  position: relative;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
}

.nominee-image-wrapper {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.nominee-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
  display: flex;
  align-items: center;
  justify-content: center;
}

.nominee-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(139, 92, 246, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

.nominee-card.modern:hover .nominee-overlay {
  opacity: 1;
}

.nominee-card.modern:hover .nominee-image {
  transform: scale(1.1);
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
}

.overlay-icon {
  font-size: 2rem;
}

.overlay-text {
  font-weight: 600;
  font-size: 1rem;
}

.rank-badge {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.nominee-status {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 255, 255, 0.9);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.status-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: #000;
}

.nominee-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.nominee-content .nominee-info {
  text-align: center;
}

.nominee-content .nominee-name {
  font-family: var(--font-accent);
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.3;
}

.nominee-content .nominee-code {
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
}

.nominee-stats {
  display: flex;
  justify-content: center;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  min-width: 140px;
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
}

.stat-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-value {
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
}

.nominee-actions {
  border-top: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.vote-section {
  width: 100%;
}

.admin-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

/* Responsive Nominee Cards */
@media (max-width: 768px) {
  .nominee-image-wrapper {
    height: 200px;
  }

  .nominees-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .stat-card {
    min-width: auto;
    width: 100%;
  }
}

/* My Submissions Page Styles */
.submissions-header {
  background: var(--surface);
  border-bottom: 1px solid var(--border);
  padding: 0.5rem;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.submissions-header .header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.submissions-title h1 {
  font-family: var(--font-accent);
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.submissions-title p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

.submissions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.8rem;
}

.submissions-stats {
  margin-bottom: var(--spacing-2xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.submissions-stats .stat-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: var(--transition-normal);
  cursor: pointer;
}

.submissions-stats .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary);
}

.submissions-stats .stat-card.events {
  border-left: 4px solid #3b82f6;
}

.submissions-stats .stat-card.categories {
  border-left: 4px solid #f59e0b;
}

.submissions-stats .stat-card.nominees {
  border-left: 4px solid #10b981;
}

.submissions-stats .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: white;
}

.submissions-stats .stat-card.events .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.submissions-stats .stat-card.categories .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.submissions-stats .stat-card.nominees .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.submissions-stats .stat-content h3 {
  font-family: var(--font-accent);
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.submissions-stats .stat-number {
  font-family: var(--font-accent);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.submissions-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.submissions-controls .search-container,
.search-container-sub {
  position: relative;
  max-width: 400px;
}

.submissions-controls .search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1rem;
}

.submissions-controls .search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 45px;
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
  transition: var(--transition-normal);
}

.submissions-controls .search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.tab-navigation {
  display: flex;
  gap: var(--spacing-sm);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-muted);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  flex: 1;
  justify-content: center;
}

.tab-btn:hover {
  color: var(--text-primary);
  background: var(--surface-light);
}

.tab-btn.active {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  box-shadow: var(--shadow-md);
}

.submissions-content {
  min-height: 400px;
}

.submissions-section {
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
}

.empty-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  font-family: var(--font-accent);
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.submissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

/* Submission Cards */
.submission-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.submission-card:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.submission-card .card-header {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.submission-card .card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: white;
}

.event-card .card-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.category-card .card-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.nominee-card .card-header {
  position: relative;
  padding: 0;
  background: none;
}

.nominee-image {
  width: 100%;
  height: 150px;
  position: relative;
}

.nominee-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--text-muted);
}

.submission-card .card-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 255, 255, 0.9);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.nominee-card .card-status {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.submission-card .card-content {
  flex: 1;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.submission-card .card-title {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.submission-card .card-description {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.submission-card .card-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: auto;
}

.submission-card .stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.submission-card .stat-icon {
  color: var(--primary);
  font-size: 1rem;
}

.submission-card .card-actions {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border);
  background: var(--surface-light);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* Responsive Submissions */
@media (max-width: 768px) {
  .submissions-header .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .submissions-controls {
    align-items: stretch;
  }

  .submissions-controls .search-container {
    max-width: none;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .tab-btn {
    justify-content: flex-start;
  }

  .submissions-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .submission-card .card-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Modern Navbar Styles */
.modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #1e90ff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 55px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  text-decoration: none;
  color: white;
}

.brand-icon {
  font-size: 1.8rem;
  color: white;
}

.brand-text {
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.navbar-desktop {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: white;
  font-weight: 500;
  transition: var(--transition-normal);
  position: relative;
}

.nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.nav-link.create-event {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-weight: 600;
}

.nav-link.create-event:hover {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.nav-icon {
  font-size: 1rem;
}

.navbar-auth {
  display: flex;
  align-items: center;
}

.auth-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.auth-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  border: none;
  font-size: 0.95rem;
}

.login-btn {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border);
}

.login-btn:hover {
  background: var(--surface);
  border-color: var(--primary);
  color: var(--primary);
}

.signup-btn {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: 2px solid transparent;
}

.signup-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.navbar-mobile {
  display: none;
}

.mobile-menu-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.mobile-menu-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.mobile-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1e90ff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.mobile-menu-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: 0.9rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  color: white;
  font-weight: 500;
  transition: var(--transition-normal);
}

.mobile-nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.mobile-nav-link.create-event {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-weight: 600;
}

.mobile-nav-link.create-event:hover {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.mobile-nav-icon {
  font-size: 1.1rem;
  width: 20px;
}

.mobile-auth {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border);
}

.mobile-auth-btn {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  border: none;
  text-align: left;
}

.mobile-auth-btn.login {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.mobile-auth-btn.login:hover {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

.mobile-auth-btn.signup {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
}

.mobile-auth-btn.signup:hover {
  opacity: 0.9;
}

.mobile-user-menu {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border);
}

/* Responsive Navbar */
@media (max-width: 768px) {
  .navbar-desktop {
    display: none;
  }

  .navbar-mobile {
    display: block;
  }

  .navbar-container {
    padding: 0 var(--spacing-md);
  }

  .brand-text {
    font-size: 1.3rem;
  }

  .brand-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .nav-links {
    gap: var(--spacing-md);
  }

  .navbar-desktop {
    gap: var(--spacing-lg);
  }
}

/* Modern Event Form Styles */
.modern-event-form-container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  border: 1px solid var(--border);
}

.form-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.form-icon {
  font-size: 2rem;
  opacity: 0.9;
}

.form-title {
  font-family: var(--font-accent);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
}

.form-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.error-alert {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: var(--spacing-md);
  margin: var(--spacing-lg);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.error-text {
  font-weight: 500;
}

.modern-event-form {
  padding: var(--spacing-xl);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-field.full-width {
  grid-column: 1 / -1;
  margin-bottom: var(--spacing-lg);
}

.field-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.label-icon {
  color: var(--primary);
  font-size: 1rem;
}

.field-input,
.field-textarea {
  padding: var(--spacing-md);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  background: var(--background);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition-normal);
  outline: none;
}

.field-input:focus,
.field-textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.field-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.image-upload-area {
  border: 2px dashed var(--border);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: var(--transition-normal);
  cursor: pointer;
  background: var(--background);
}

.image-upload-area:hover,
.image-upload-area.drag-over {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.upload-icon {
  font-size: 3rem;
  color: var(--text-muted);
}

.upload-placeholder h4 {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.upload-placeholder p {
  color: var(--text-secondary);
  margin: 0;
}

.upload-btn {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.upload-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.image-preview-section {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

.image-preview-section:hover .image-overlay {
  opacity: 1;
}

.change-image-btn {
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition-normal);
}

.change-image-btn:hover {
  background: white;
  transform: translateY(-1px);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border);
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 140px;
  justify-content: center;
}

.btn-icon {
  font-size: 0.9rem;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Form */
@media (max-width: 768px) {
  .modern-event-form-container {
    margin: var(--spacing-md);
    border-radius: var(--radius-lg);
  }

  .form-header {
    padding: var(--spacing-lg);
  }

  .form-title-section {
    gap: var(--spacing-md);
  }

  .form-icon {
    font-size: 1.5rem;
  }

  .form-title {
    font-size: 1.5rem;
  }

  .modern-event-form {
    padding: var(--spacing-lg);
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .form-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Edit Form Section */
.edit-form-section {
  background: var(--background);
  padding: var(--spacing-2xl) 0;
  border-top: 1px solid var(--border);
  margin-top: var(--spacing-2xl);
}

.edit-form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Enhanced Event Admin Actions */
.event-admin-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.event-admin-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: 600;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.event-admin-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  font-size: 0.9rem;
}

.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.hero-main-image {
  width: 100%;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  transition: var(--transition-normal);
}

.hero-main-image:hover {
  transform: scale(1.02);
}

.hero-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-card {
  position: absolute;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  animation: float 3s ease-in-out infinite;
}

.card-1 {
  top: 10%;
  right: -10%;
  animation-delay: 0s;
}

.card-2 {
  bottom: 30%;
  left: -15%;
  animation-delay: 1s;
}

.card-3 {
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.card-icon {
  color: var(--primary);
  font-size: 1.5rem;
  width: 24px;
  flex-shrink: 0;
}

.card-content h4 {
  font-family: var(--font-accent);
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.card-content p {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin: 0;
}

/* Responsive Hero Section */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .hero-text {
    order: 2;
  }

  .hero-image {
    order: 1;
  }

  .hero-features {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .hero-actions {
    justify-content: center;
  }

  .floating-card {
    display: none;
  }

  .hero-description {
    max-width: none;
  }
}

.paystack-btn {
  height: 5vh;
  font-size: 1rem;
  font-weight: 600;
  border: 1px solid #111;
}

/* Search Bar */
.search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto 0.9rem;
}

.search-bar {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-left: 3rem;
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 1rem;
  font-family: var(--font-primary);
  outline: none;
  transition: var(--transition-normal);
}

.search-bar:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-bar::placeholder {
  color: var(--text-muted);
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.1rem;
}

footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: #1e90ff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 5%;
  box-shadow: rgba(0, 0, 0, 0.35) 0 -50px 36px -28px inset;
}

.thumbs-wrapper.axis-vertical {
  display: none !important;
}

.all-container {
  border: 1px solid #fff;
  background-color: #fff;
  margin: 2% 0;
}

.all-container div img {
  width: 10%;
  height: 10%;
}

.all-container div {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1%;
  border: 1px solid #000;
  margin-bottom: 1%;
}

.all-container div h3 {
  font-size: 0.6rem;
  font-weight: 900;
  line-height: 1.2;
}

.all-container button {
  height: 3vh;
  background-color: red;
  color: #fff;
  font-size: 0.5rem;
  font-weight: 700;
  border: none;
  padding: 0 1%;
}

.add-data button {
  width: 100%;
  background-color: green;
  border: none;
  font-size: 1.1rem;
  font-weight: 900;
  margin-top: 2%;
  cursor: pointer;
}

.winner {
  background-color: green;
  color: white;
}

.loading {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 5%;
}

.endvote {
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  display: block;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 900000;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5%;
  padding: 0 5%;
}

.marquee {
  display: inline-block;
  white-space: nowrap;
  position: relative;
  animation: marquee 35s linear infinite;
}

.marquee p {
  display: inline-block;
  text-align: left;
  font-size: 1.2rem;
  font-weight: 800;
  font-style: italic;
}

.results-page h1,
.no-res {
  color: whitesmoke;
}

.results-page {
  padding-bottom: 5rem;
  text-align: center;
}

.results-page select {
  margin-bottom: 20px;
  padding: 10px;
  width: 80%;
  border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  font-size: 1.2rem;
}

.chart-container {
  width: 95%;
}

.no-results {
  background-color: #fff;
  margin-top: -4%;
}

.no-results h2 {
  text-align: center;
  padding: 3%;
}

.no-results img {
  width: 100%;
  height: 100%;
}

@keyframes marquee {
  0% {
      transform: translateX(100%);
  }
  100% {
      transform: translateX(-100%);
  }
}

@media only screen and (min-width: 768px) {
  .nav-container .nav-inner {
    width: 30%;
    height: 9vh;
    left: 35%;
    top: 40%;
  }

  .search-bar {
    width: 35%;
    margin-top: 1%;
  }

  .home-title-container {
    width: 100%;
  }

  .home-container {
    width: 90%;
    margin: 0 auto;
  }

  .search-container {
    max-width: 1000px;
  }

  .search-icon {
    left: 35%;
    top: 60%;
  }

  .home-title-container h2,
  .home-title-container h3 {
    font-size: 2rem;
  }

  .links {
    width: 15%;
    height: 23vh;
    padding: 1%;
  }

  .links h2 {
    font-size: 1.2rem;
  }

  .vote-form-modal {
  width: 46%;
  left: 25%;
  }

  footer p {
    font-size: 1.1rem;
    line-height: 1.5;
  }

  .nominees-card {
    width: 90%;
    margin: 0 auto;
  }

  .nominees-card .nominees-title {
    margin: 3% 0;
  }

  .card-container .card-container-inner {
    width: 13rem;
  }

  .endvote h2,
  .endvote p {
    font-size: 1.5rem;
  }

  .card-container div h2 {
    font-size: 1.2rem;
  }

  .vote-form {
    width: 45%;
  }

  .form-div .close-btn {
    left: 67%;
    top: 28%;
  }

  .add-data {
    width: 30%;
  }

  .add-data button {
    padding: 2% 0;
  }

  .over-main {
    width: 95%;
    margin: 0 auto;
    padding-bottom: 60px;
  }

  .all-main-container {
    display: flex;
    gap: 5%;
  }

  .all-container {
    width: 70%;
  }

  .all-container div {
    justify-content: flex-start;
    padding: 0 0.3%;
  }

  .all-container button {
    font-size: 0.9rem;
    cursor: pointer;
  }

  .all-container div h3 {
    font-size: 1rem;
    font-weight: 400;
    margin-right: auto;
  }
}

/* Multi-Event Components Styles */

/* Events Container */
.events-container {
  display: grid;
}

.events-title-container {
  text-align: center;
}

.events-title-container h1 {
  font-family: var(--font-accent);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.events-title-container h2 {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  font-weight: 400;
  margin-bottom: var(--spacing-lg);
}

/* Events Grid */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

/* Event Card */
.event-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.event-card:hover::before {
  transform: scaleX(1);
}

.event-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.event-image-wrapper {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.event-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
}

.event-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

.event-card:hover .event-overlay {
  opacity: 1;
}

.event-info {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-title {
  font-family: var(--font-accent);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.event-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
  flex: 1;
}

.event-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.voting-price,
.category-count {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.85rem;
  color: var(--text-muted);
}

.voting-price span {
  color: var(--success);
  font-weight: 500;
}

.category-count span {
  color: var(--warning);
  font-weight: 500;
}

/* Event Detail Styles */
.event-detail-container {
  display: grid;
  gap: 0.5rem;
}

.event-header {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
}

.event-header-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.event-image-section {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  flex-shrink: 0;
}

.event-detail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-detail-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
}

.event-header-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.event-detail-title {
  font-family: var(--font-accent);
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.event-detail-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
}

.event-detail-meta {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.voting-price-detail,
.category-count-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1rem;
  color: var(--text-muted);
}

.price-text {
  color: var(--success);
  font-weight: 600;
}

/* Categories Section */
.categories-section {
  display: grid;
  gap: var(--spacing-xl);
}

.section-header {
  text-align: center;
}

.section-header h2 {
  font-family: var(--font-accent);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.section-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Category Detail Styles */
.category-detail-container {
  display: grid;
}

.category-header {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: 0.5rem;
}

.category-header-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.category-icon-section {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--warning), #fbbf24);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-header-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.category-detail-title {
  font-family: var(--font-accent);
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.category-detail-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
}

.voting-info {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.voting-price-info,
.nominee-count-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--text-muted);
}

.voting-price-info span {
  color: var(--success);
  font-weight: 600;
}

.nominee-count-info span {
  color: var(--text-secondary);
  font-weight: 500;
}

/* Nominees Section */
.nominees-section {
  display: grid;
  gap: var(--spacing-xl);
}

.nominees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.nominee-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.nominee-card:hover::before {
  transform: scaleX(1);
}

.nominee-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-light);
}

.nominee-image-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.nominee-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.nominee-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--surface-light), var(--surface));
}

.nominee-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  flex: 1;
}

.nominee-name {
  font-family: var(--font-accent);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.nominee-code {
  color: var(--text-muted);
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
  background: var(--surface-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  display: inline-block;
  width: fit-content;
}

.nominee-votes {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.vote-count {
  color: var(--success);
  font-weight: 600;
  font-size: 1.1rem;
}

.nominee-voting {
  margin-top: auto;
}

/* Error Container */
.error-container {
  text-align: center;
  padding: var(--spacing-2xl);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  max-width: 500px;
  margin: 0 auto;
}

.error-container h2 {
  color: var(--error);
  margin-bottom: var(--spacing-md);
}

.error-container p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .event-header-content,
  .category-header-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .event-image-section {
    width: 100px;
    height: 100px;
    margin: 0 auto;
  }

  .category-icon-section {
    width: 60px;
    height: 60px;
    margin: 0 auto;
  }

  .event-detail-title,
  .category-detail-title {
    font-size: 1.5rem;
  }

  .events-grid,
  .nominees-grid {
    grid-template-columns: 1fr;
  }

  .event-detail-meta,
  .voting-info {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

/* --- Admin Event Creation Wizard Styles --- */
.event-wizard-container {
  max-width: 600px;
  margin: 2rem auto;
  background: var(--surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: 2rem 2.5rem 2.5rem 2.5rem;
}

.event-form, .category-form, .nominee-form {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  background: none;
}

.event-form h2, .category-form-container h2, .added-categories-list h3, .nominee-form h4 {
  font-family: var(--font-accent);
  color: var(--primary-light);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.2rem;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="file"],
.form-group textarea,
.form-input {
  padding: 0.6rem 0.8rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  background: var(--surface-light);
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
}

.form-group input[type="file"] {
  background: none;
  color: var(--text-secondary);
  padding: 0.3rem 0.2rem;
}

.form-group textarea {
  min-height: 70px;
  resize: vertical;
}

.btn {
  padding: 0.6rem 1.2rem;
  border-radius: var(--radius-md);
  border: none;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-top: 0.2rem;
}

.btn-primary {
  background: var(--primary);
  color: #fff;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--surface-light);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  margin: 0 1rem 1rem 1rem;
}

.btn-secondary:hover {
  background: var(--surface);
}

.btn-success {
  background: var(--success);
  color: #fff;
}

.btn-success:hover {
  background: #059669;
}

.btn-sm {
  font-size: 0.95rem;
  padding: 0.4rem 0.8rem;
}

.error-container {
  background: var(--error);
  color: #fff;
  padding: 0.7rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 0.7rem;
  font-size: 1rem;
}

.category-form-container {
  margin-top: 2rem;
}

.added-categories-list {
  margin-top: 2rem;
  background: var(--surface-light);
  border-radius: var(--radius-lg);
  padding: 1.2rem 1.5rem;
}

.added-category {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-light);
}

.category-header-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.nominee-list {
  margin: 0.5rem 0 0 1.2rem;
  padding: 0;
  list-style: disc;
  color: var(--text-primary);
}

.nominee-list li {
  margin-bottom: 0.2rem;
}

@media (max-width: 700px) {
  .event-wizard-container {
    padding: 1rem 0.5rem 2rem 0.5rem;
  }
  .added-categories-list {
    padding: 0.7rem 0.5rem;
  }
}

/* Image Preview Styles */
.image-preview-container {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--surface-light);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
}

.image-preview-container h4,
.image-preview-container h5 {
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.image-preview {
  max-width: 100%;
  max-height: 200px;
  border-radius: var(--radius-md);
  border: 2px solid var(--border);
  object-fit: cover;
  display: block;
  margin: 0 auto;
  transition: var(--transition-normal);
}

.image-preview:hover {
  transform: scale(1.02);
  border-color: var(--primary);
}

/* Enhanced File Input Styling */
.form-group input[type="file"] {
  padding: 0.5rem;
  border: 2px dashed var(--border);
  border-radius: var(--radius-md);
  background: var(--surface);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.form-group input[type="file"]:hover {
  border-color: var(--primary);
  background: var(--surface-light);
}

.form-group input[type="file"]:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

@media (max-width: 700px) {
  .image-preview {
    max-height: 150px;
  }
  
  .image-preview-container {
    padding: 0.75rem;
  }
}

/* Authentication Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.auth-particles {
  position: relative;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
}

.auth-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.auth-logo {
  text-align: center;
  margin-bottom: 0.5rem;
  color: white;
}

.auth-logo h1 {
  font-size: 2.5rem;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auth-logo p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.auth-form-container {
  width: 100%;
}

.auth-form-card {
  background: white;
  border-radius: 20px;
  padding: 0.9rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.auth-header p {
  color: #666;
  font-size: 1rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 15px 45px 15px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #ff4757;
  background: #fff5f5;
}

.input-icon {
  position: absolute;
  right: 15px;
  font-size: 1.2rem;
  color: #999;
  pointer-events: none;
}

.password-toggle {
  position: absolute;
  right: 45px;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.password-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.validation-error {
  color: #ff4757;
  font-size: 0.8rem;
  margin-top: 5px;
}

.error-message {
  background: #fff5f5;
  color: #ff4757;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #fed7d7;
  font-size: 0.9rem;
  text-align: center;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-btn.loading {
  cursor: wait;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.auth-footer {
  text-align: center;
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid #e1e5e9;
}

.auth-footer p {
  color: #666;
  font-size: 0.9rem;
}

.switch-btn {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.switch-btn:hover {
  color: #764ba2;
}

/* User Menu Styles */
.user-menu {
  position: relative;
  display: inline-block;
}

.user-menu-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 8px 16px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.user-menu-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.user-avatar {
  font-size: 1.2rem;
}

.user-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.menu-arrow {
  font-size: 0.8rem;
  transition: transform 0.2s ease;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 10px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
}

.user-info {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-large {
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.username {
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 4px 0;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
  margin: 0;
}

.menu-divider {
  height: 1px;
  background: #e1e5e9;
  margin: 10px 0;
}

.menu-items {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
  font-size: 0.9rem;
  text-decoration: none;
  text-align: left;
}

.menu-item:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.menu-item.admin-link {
  color: var(--primary);
  font-weight: 600;
}

.menu-item.admin-link:hover {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.menu-item.admin-link .menu-icon {
  color: var(--primary);
}

.menu-item.create-event-link {
  color: #10b981;
  font-weight: 600;
}

.menu-item.create-event-link:hover {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.menu-item.create-event-link .menu-icon {
  color: #10b981;
}

.menu-icon {
  font-size: 1rem;
  color: #6b7280;
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn {
  color: #dc2626;
  border-top: 1px solid #e5e7eb;
  margin-top: 5px;
}

.logout-btn:hover {
  background-color: #fef2f2;
}

.logout-btn .menu-icon {
  color: #dc2626;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-content {
    padding: 10px;
  }
  
  .auth-logo h1 {
    font-size: 2rem;
  }
  
  .auth-header h2 {
    font-size: 1.5rem;
  }
  
  .user-dropdown {
    right: -50px;
    min-width: 180px;
  }
  
  .header-nav {
    padding: 10px 15px;
  }
  
  .nav-brand h2 {
    font-size: 1.2rem;
  }
  
  .auth-buttons {
    gap: 8px;
  }
  
  .auth-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* Header Navigation Styles */
.header-container {
  position: relative;
}

.header-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(10px);
}

.nav-brand h2 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auth-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.auth-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.login-btn {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.signup-btn {
  background: white;
  color: #667eea;
}

.signup-btn:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Main Navbar Styles */
.main-navbar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  padding: 0 30px;
  min-height: 60px;
  position: relative;
  z-index: 200;
}

.nav-brand {
  color: #fff;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  letter-spacing: 1px;
  user-select: none;
}

.nav-desktop {
  display: flex;
  align-items: center;
  gap: 24px;
}

.nav-link {
  color: #fff;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 8px 14px;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
  transition: background 0.2s, color 0.2s;
}

.nav-link:hover, .nav-link.active {
  background: rgba(255,255,255,0.12);
  color: #ffe082;
}

/* Hide sign up button everywhere */
.signup-btn, .dropdown-signup { display: none !important; }

.nav-mobile {
  display: none;
}

/* Dropdown styles for mobile */
.nav-hamburger {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.2s;
}

.nav-hamburger:hover {
  background: rgba(255,255,255,0.1);
}

.nav-dropdown {
  position: absolute;
  top: 60px;
  right: 20px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  min-width: 180px;
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  z-index: 999;
  animation: dropdownIn 0.2s;
}

@keyframes dropdownIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dropdown-link {
  color: #667eea;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 10px 24px;
  text-align: left;
  text-decoration: none;
  cursor: pointer;
  border-radius: 0;
  transition: background 0.2s, color 0.2s;
  width: 100%;
  display: block;
}

.dropdown-link:hover, .dropdown-link.active {
  background: #f3f4f6;
  color: #764ba2;
}

.dropdown-user-menu {
  margin-top: 10px;
  padding: 0 16px;
}

/* Responsive: show hamburger and dropdown on mobile, row on desktop */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }
  .nav-mobile {
    display: flex;
    align-items: center;
  }
  .main-navbar {
    padding: 0 12px;
    min-height: 54px;
  }
  .nav-brand {
    font-size: 1.1rem;
  }
}

@media (min-width: 769px) {
  .nav-mobile {
    display: none;
  }
  .nav-desktop {
    display: flex;
  }
}

/* Modern Category Form Styles */
.modern-category-form-container {
  max-width: 1000px;
  margin: 0 auto;
  background: var(--background);
  min-height: 100vh;
}

.category-form-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: 1.2rem;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.header-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.header-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.header-title {
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
}

.header-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.category-form-content {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.add-category-section,
.categories-list-section {
  background: var(--surface);
  border-radius: var(--radius-xl);
  padding: 0.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
}

.section-header {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-family: var(--font-accent);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.section-icon {
  color: var(--primary);
  font-size: 1.3rem;
}

.modern-category-form {
  display: flex;
  flex-direction: column;
}

.categories-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.category-card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-normal);
}

.category-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary);
}

.category-card-header {
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--surface-light);
}

.category-info {
  flex: 1;
}

.category-name {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.category-description {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.95rem;
}

.category-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.expand-btn {
  background: var(--primary);
  border: none;
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
}

.expand-btn:hover {
  background: var(--primary-dark);
}

.category-card-content {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border);
  overflow: hidden;
}

.category-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-icon {
  color: var(--primary);
}

.nominees-list {
  margin-bottom: var(--spacing-lg);
}

.nominees-list h4 {
  font-family: var(--font-accent);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.nominees-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.nominee-tag {
  background: var(--primary);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.85rem;
  font-weight: 500;
}

.category-card-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.nominee-form-section {
  background: var(--surface);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--border);
}

/* Modern Footer Styles */
.modern-footer {
  background: #1e90ff;
  color: white;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.footer-brand-icon {
  font-size: 2rem;
  color: white;
}

.footer-brand-text {
  font-family: var(--font-accent);
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
}

.footer-description {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-sm);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: var(--transition-normal);
  font-size: 1.1rem;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
}

.footer-section-title {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin: 0 0 var(--spacing-md) 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-links li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition-normal);
  font-size: 0.95rem;
}

.footer-links li a:hover {
  color: white;
  padding-left: var(--spacing-xs);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

.contact-icon {
  color: white;
  font-size: 1rem;
  width: 16px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 0;
}

.footer-bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.copyright {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 0.9rem;
}

.made-with {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 0.9rem;
}

.heart-icon {
  color: #ff6b6b;
  font-size: 0.8rem;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
  }

  .footer-section {
    text-align: center;
  }

  .brand-section {
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .contact-info {
    align-items: center;
  }

  .footer-links {
    align-items: center;
  }
}

@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
  }
}
