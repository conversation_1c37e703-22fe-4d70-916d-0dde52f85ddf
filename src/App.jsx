import './App.css';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Provider } from 'react-redux';
import store from './reudx/store';

// New multi-event components
import EventList from './components/EventList';
import EventDetail from './components/EventDetail';
import CategoryDetail from './components/CategoryDetail';
import ResultsPage from './components/ResultsPage';
import Footer from './components/Footer';
import HeroSection from './components/ImageSlider';
import EventWizard from './components/admin/EventWizard';
import AdminDashboard from './components/admin/AdminDashboard';
import MySubmissions from './components/MySubmissions';

// Authentication components
import AuthPage from './components/auth/AuthPage';
import ProtectedRoute from './components/auth/ProtectedRoute';

const App = () => (
  <Provider store={store}>
    <BrowserRouter>
      <HeroSection />
      <ToastContainer />
      <Routes>
        {/* Public routes */}
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/" element={<EventList />} />
        <Route path="/events/:id" element={<EventDetail />} />
        <Route path="/events/:eventId/categories/:categoryId" element={<CategoryDetail />} />
        <Route path="/results" element={<ResultsPage />} />
        <Route path="/my-submissions" element={<ProtectedRoute><MySubmissions /></ProtectedRoute>} />
        
        {/* Protected admin routes */}
        <Route path="/admin/create-event" element={
          <ProtectedRoute requireAuth={true}>
            <EventWizard />
          </ProtectedRoute>
        } />
        <Route path="/admin/dashboard" element={<AdminDashboard />} />
      </Routes>
      <Footer />
    </BrowserRouter>
  </Provider>
);

export default App;
