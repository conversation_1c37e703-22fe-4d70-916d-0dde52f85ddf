import './App.css';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import HomePageRenderer from './components/HomePage';
import BestClique from './components/categories/BestClique';
import BestFoodJoint from './components/categories/BestFoodJoint';
import PerfectGentleman from './components/categories/PerfectGentleman';
import FaceOfBoass from './components/categories/FaceOfBoass';
import BestRapper from './components/categories/BestRapper';
import MostFashionable from './components/categories/MostFashionable';
import BestSinger from './components/categories/BestSinger';
import BestArtist from './components/categories/BestArtist';
import Photogenic from './components/categories/Photogenic';
import BestHouse from './components/categories/BestHouse';
import BestDepartment from './components/categories/BestDepartment';
import BestComedian from './components/categories/BestComedian';
import SwaggerKing from './components/categories/SwaggerKing';
import SwaggerQueen from './components/categories/SwaggerQueen';
import BestDancer from './components/categories/BestDancer';
import PressureKing from './components/categories/PressureKing';
import PressureQueen from './components/categories/PressureQueen';
import MostPopular from './components/categories/MostPopular';
import MostCreative from './components/categories/MostCreative';
import FattestWallet from './components/categories/FattestWallet';
import Footer from './components/Footer';
import ImageSlider from './components/ImageSlider';
import AllNominees from './components/AllNominees';
import ResultsPage from './components/ResultsPage';
import NoResults from './components/helpers/NoResults';

const App = () => (
  <BrowserRouter>
    {/* <Navigation /> */}
    <ImageSlider />
    <ToastContainer />
    <Routes>
      <Route path="/" element={<HomePageRenderer />} />
      <Route path="/bestclique" element={<BestClique />} />
      <Route path="/bestfoodjoint" element={<BestFoodJoint />} />
      <Route path="/perfectgentleman" element={<PerfectGentleman />} />
      <Route path="/faceofboass" element={<FaceOfBoass />} />
      <Route path="/bestrapper" element={<BestRapper />} />
      <Route path="/mostfashionable" element={<MostFashionable />} />
      <Route path="/bestsinger" element={<BestSinger />} />
      <Route path="/bestartist" element={<BestArtist />} />
      <Route path="/photogenic" element={<Photogenic />} />
      <Route path="/besthouse" element={<BestHouse />} />
      <Route path="/bestdepartment" element={<BestDepartment />} />
      <Route path="/bestcomedian" element={<BestComedian />} />
      <Route path="/swaggerking" element={<SwaggerKing />} />
      <Route path="/swaggerqueen" element={<SwaggerQueen />} />
      <Route path="/bestdancer" element={<BestDancer />} />
      <Route path="/pressureking" element={<PressureKing />} />
      <Route path="/pressurequeen" element={<PressureQueen />} />
      <Route path="/mostpopular" element={<MostPopular />} />
      <Route path="/mostcreative" element={<MostCreative />} />
      <Route path="/fattestwallet" element={<FattestWallet />} />
      <Route path="/all" element={<AllNominees />} />
      <Route path="/results" element={<NoResults />} />
      <Route path="/outcome" element={<ResultsPage />} />
    </Routes>
    <Footer />
  </BrowserRouter>
);

export default App;
