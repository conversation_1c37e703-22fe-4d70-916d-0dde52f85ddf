import './App.css';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import HomePageRenderer from './components/HomePage';
import Influencer from './components/categories/Influecer';
import Djs from './components/categories/Djs';
import RadioStation from './components/categories/RadioStation';
import RadioProgram from './components/categories/RadioProgram';
import HardworkingYouth from './components/categories/HardworkingYouth';
import YouthEntrepreneur from './components/categories/YouthEntrepreneur';
import MostPopularPersonality from './components/categories/MostPopularPersonality';
import RadioPersonality from './components/categories/RadioPersonality';
import RadioSportPersonality from './components/categories/RadioSportPersonality';
import FemaleNewsCaster from './components/categories/FemaleNewsCaster';
import MaleNewsCaster from './components/categories/MaleNewsCaster';
import MaleFashionDesigner from './components/categories/MaleFashionDesigner';
import FemaleFashionDesigner from './components/categories/FemaleFashionDesigner';
import GraphicDesigner from './components/categories/GraphicDesigner';
import Photographer from './components/categories/Photographer';
import Dancer from './components/categories/Dancer';
import DanceGroup from './components/categories/DanceGroup';
import Rapper from './components/categories/Rapper';
import Producer from './components/categories/Producer';
import HiplifeHiphopArtist from './components/categories/HiplifeHiphopArtist';
import DancehallArtist from './components/categories/DancehallArtist';
import GospelArtist from './components/categories/GospelArtist';
import FemaleVocalist from './components/categories/FemaleVocalist';
import MaleVocalist from './components/categories/MaleVocalist';
import Artiste from './components/categories/Artiste';
import PopularSong from './components/categories/PopularSong';
import MusicVideo from './components/categories/MusicVideo';
import VideoDirector from './components/categories/VideoDirector';
import SocialMediaPersonality from './components/categories/SocialMediaPersonality';
import Event from './components/categories/Event';
import MakeUpArtiste from './components/categories/MakeUpArtiste';
import HairStylist from './components/categories/HairStylist';
import Barber from './components/categories/Barber';
import KeepfitClub from './components/categories/KeepfitClub';
import Mc from './components/categories/Mc';
import Footer from './components/Footer';
import ImageSlider from './components/ImageSlider';
import AllNominees from './components/AllNominees';
import Restaurant from './components/categories/Restaurant';
import HerbalClinic from './components/categories/HerbalClinic';
import RadioPoliticalShow from './components/categories/RadioPoliticalShow';
import RadioPoliticalShowHost from './components/categories/RadioPoliticalShowHost';
import YouthPoliticalAdvocate from './components/categories/YouthPoliticalAdvocate';
import ReggaeDancehallSong from './components/categories/ReggaeDancehallSong';
import RadioPresenter from './components/categories/RadioPresenter';
import RadioSportsHost from './components/categories/RadioSportsHost';
import RadioSportsForeignNewsPresenter from './components/categories/RadioSportsForeignNewsPresenter';
import RadioSportsAnalyst from './components/categories/RadioSportsAnalyst';
import RadioSportsProgram from './components/categories/RadioSportsProgram';
import RadioEntertainmentShow from './components/categories/RadioEntertainmentShow';
import EventDecorator from './components/categories/EventDecorator';
import ResultsPage from './components/ResultsPage';
import NewArtist from './components/categories/NewArtist';
import MusicGroup from './components/categories/MusicGroup';
import HiphopHiplifeSong from './components/categories/HiphopHiplifeSong';
import NoResults from './components/helpers/NoResults';

const App = () => (
  <BrowserRouter>
    {/* <Navigation /> */}
    <ImageSlider />
    <ToastContainer />
    <Routes>
      <Route path="/" element={<HomePageRenderer />} />
      <Route path="/influencer" element={<Influencer />} />
      <Route path="/djs" element={<Djs />} />
      <Route path="/radiostation" element={<RadioStation />} />
      <Route path="/radioprogram" element={<RadioProgram />} />
      <Route path="/hardworkingyouth" element={<HardworkingYouth />} />
      <Route path="/youthentrepreneur" element={<YouthEntrepreneur />} />
      <Route path="/mostpopularpersonality" element={<MostPopularPersonality />} />
      <Route path="/radiopersonality" element={<RadioPersonality />} />
      <Route path="/radiosportpersonality" element={<RadioSportPersonality />} />
      <Route path="/femalenewscaster" element={<FemaleNewsCaster />} />
      <Route path="/malenewscaster" element={<MaleNewsCaster />} />
      <Route path="/malefashiondesigner" element={<MaleFashionDesigner />} />
      <Route path="/femalefashiondesigner" element={<FemaleFashionDesigner />} />
      <Route path="/graphicdesigner" element={<GraphicDesigner />} />
      <Route path="/photographer" element={<Photographer />} />
      <Route path="/dancer" element={<Dancer />} />
      <Route path="/dancegroup" element={<DanceGroup />} />
      <Route path="/rapper" element={<Rapper />} />
      <Route path="/producer" element={<Producer />} />
      <Route path="/hiplifehiphop" element={<HiplifeHiphopArtist />} />
      <Route path="/dancehallartist" element={<DancehallArtist />} />
      <Route path="/gospelartist" element={<GospelArtist />} />
      <Route path="/femalevocalist" element={<FemaleVocalist />} />
      <Route path="/malevocalist" element={<MaleVocalist />} />
      <Route path="/artiste" element={<Artiste />} />
      <Route path="/popularsong" element={<PopularSong />} />
      <Route path="/musicvideo" element={<MusicVideo />} />
      <Route path="/videodirector" element={<VideoDirector />} />
      <Route path="/socialmediapersonality" element={<SocialMediaPersonality />} />
      <Route path="/event" element={<Event />} />
      <Route path="/makeupartiste" element={<MakeUpArtiste />} />
      <Route path="/hairstylist" element={<HairStylist />} />
      <Route path="/barber" element={<Barber />} />
      <Route path="/keepfitclub" element={<KeepfitClub />} />
      <Route path="/mc" element={<Mc />} />
      <Route path="/restaurant" element={<Restaurant />} />
      <Route path="/herbalclinic" element={<HerbalClinic />} />
      <Route path="/radiopoliticalshow" element={<RadioPoliticalShow />} />
      <Route path="/radiopoliticalshowhost" element={<RadioPoliticalShowHost />} />
      <Route path="/youthpoliticaladvocate" element={<YouthPoliticalAdvocate />} />
      <Route path="/reggaedancehallsong" element={<ReggaeDancehallSong />} />
      <Route path="/radiopresenter" element={<RadioPresenter />} />
      <Route path="/radiosporthost" element={<RadioSportsHost />} />
      <Route path="/radiosportsforeignnewspresenter" element={<RadioSportsForeignNewsPresenter />} />
      <Route path="/radiosportsanalyst" element={<RadioSportsAnalyst />} />
      <Route path="/radiosportsprogram" element={<RadioSportsProgram />} />
      <Route path="/radioentertainmentshow" element={<RadioEntertainmentShow />} />
      <Route path="/eventdecorator" element={<EventDecorator />} />
      <Route path="/all" element={<AllNominees />} />
      <Route path="/results" element={<NoResults />} />
      <Route path="/outcome" element={<ResultsPage />} />
      <Route path="/newartist" element={<NewArtist />} />
      <Route path="/musicgroup" element={<MusicGroup />} />
      <Route path="/hiphophiplifesong" element={<HiphopHiplifeSong />} />
    </Routes>
    <Footer />
  </BrowserRouter>
);

export default App;
