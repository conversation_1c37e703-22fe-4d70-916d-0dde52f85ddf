import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/ApiUrl';

const initialState = {
  nominees: [],
  status: 'idle',
  error: null,
};

export const fetchNominees = createAsyncThunk('nominee/fetchNominees', async () => {
  try {
    const response = await axios.get(`${ApiUrl}/nominees`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch nominees.');
  }
});

export const createNominee = createAsyncThunk('nominee/createNominee', async (nominee) => {
  try {
    const response = await axios.post(`${ApiUrl}/nominees`, nominee);
    return response.data;
  } catch (error) {
    throw new Error('Failed to create nominee.');
  }
});

export const deleteNominee = createAsyncThunk('nominee/deleteNominee', async (nomineeId) => {
  try {
    await axios.delete(`${ApiUrl}/nominees/${nomineeId}`);
    return nomineeId;
  } catch (error) {
    throw new Error('Failed to delete nominee.');
  }
});

export const fetchNomineesByCategory = createAsyncThunk('nominees/fetchNomineesByCategory', async (category) => {
  try {
    const response = await axios.get(`${ApiUrl}/nominees/${category}`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch nominees.');
  }
});

export const updateVote = createAsyncThunk('nominees/updateVote', async (nominee) => {
  try {
    const { id, ...updatedData } = nominee;
    const response = await axios.put(`${ApiUrl}/nominees/${id}/increment_votes`, updatedData);
    return response.data;
  } catch (error) {
    throw new Error('Failed to update vote.');
  }
});

const nomineeSlice = createSlice({
  name: 'nominees',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchNomineesByCategory.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchNomineesByCategory.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        nominees: action.payload,
      }))
      .addCase(fetchNomineesByCategory.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.error.message,
      }))
      .addCase(updateVote.fulfilled, (state, action) => ({
        ...state,
        nominees: state.nominees.map((nominee) => {
          if (nominee.id === action.payload.id) {
            return {
              ...nominee,
              ...action.payload,
            };
          }
          return nominee;
        }),
      }))
      .addCase(fetchNominees.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchNominees.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        nominees: action.payload,
      }))
      .addCase(deleteNominee.fulfilled, (state, action) => ({
        ...state,
        nominees: state.nominees.filter((nominee) => nominee.id !== action.payload),
      }))
      .addCase(createNominee.fulfilled, (state, action) => {
        state.nominees.push(action.payload);
      });
  },
});

export default nomineeSlice.reducer;

export const selectNominees = (state) => state.nominees.nominees;
export const selectNomineesStatus = (state) => state.nominees.status;
export const selectNomineesError = (state) => state.nominees.error;
