import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import apiService from '../../services/api';

const initialState = {
  nominees: [],
  status: 'idle',
  error: null,
};

export const fetchNomineesByEvent = createAsyncThunk('nominees/fetchNomineesByEvent', async (eventId) => {
  try {
    return await apiService.getNominees(eventId);
  } catch (error) {
    throw new Error('Failed to fetch nominees');
  }
});

export const fetchNomineesByCategory = createAsyncThunk('nominees/fetchNomineesByCategory', async ({ eventId, categoryId }) => {
  try {
    return await apiService.getNomineesByCategory(eventId, categoryId);
  } catch (error) {
    throw new Error('Failed to fetch nominees');
  }
});


export const createNomineeWithImage = createAsyncThunk('nominees/createNomineeWithImage', async ({ eventId, categoryId, formData }) => {
  try {
    return await apiService.createNomineeWithImage(eventId, categoryId, formData);
  } catch (error) {
    throw new Error('Failed to create nominee');
  }
});

export const incrementVotes = createAsyncThunk('nominees/incrementVotes', async ({ nomineeId, incrementBy = 1 }) => {
  try {
    return await apiService.incrementVotes(nomineeId, incrementBy);
  } catch (error) {
    throw new Error('Failed to increment votes');
  }
});

export const updateNominee = createAsyncThunk('nominees/updateNominee', async ({ eventId, categoryId, nomineeId, nomineeData }) => {
  try {
    return await apiService.updateNominee(eventId, categoryId, nomineeId, nomineeData);
  } catch (error) {
    throw new Error('Failed to update nominee');
  }
});

export const deleteNominee = createAsyncThunk('nominees/deleteNominee', async ({ eventId, categoryId, nomineeId }) => {
  try {
    return await apiService.deleteNominee(eventId, categoryId, nomineeId);
  } catch (error) {
    throw new Error('Failed to delete nominee');
  }
});

const nomineesSlice = createSlice({
  name: 'nominees',
  initialState,
  reducers: {
    clearNominees: (state) => {
      state.nominees = [];
    },
    addNominee: (state, action) => {
      state.nominees.push(action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchNomineesByEvent.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchNomineesByEvent.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.nominees = action.payload;
        state.error = null;
      })
      .addCase(fetchNomineesByEvent.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(fetchNomineesByCategory.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchNomineesByCategory.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.nominees = action.payload;
        state.error = null;
      })
      .addCase(fetchNomineesByCategory.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(createNomineeWithImage.fulfilled, (state, action) => {
        state.nominees.push(action.payload);
        state.error = null;
      })
      .addCase(incrementVotes.fulfilled, (state, action) => {
        state.nominees = state.nominees.map((nominee) => {
          if (nominee.id === action.payload.id) {
            return {
              ...nominee,
              vote: action.payload.vote,
            };
          }
          return nominee;
        });
      })
      .addCase(updateNominee.fulfilled, (state, action) => {
        state.nominees = state.nominees.map((nominee) => {
          if (nominee.id === action.payload.id) {
            return action.payload;
          }
          return nominee;
        });
        state.error = null;
      })
      .addCase(deleteNominee.fulfilled, (state, action) => {
        state.nominees = state.nominees.filter((nominee) => nominee.id !== action.payload.id);
        state.error = null;
      });
  },
});

export const { clearNominees, addNominee } = nomineesSlice.actions;

export const selectNominees = (state) => state.nominees.nominees;
export const selectNomineesStatus = (state) => state.nominees.status;
export const selectNomineesError = (state) => state.nominees.error;

export default nomineesSlice.reducer; 