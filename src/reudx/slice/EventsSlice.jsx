import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import apiService from '../../services/api';

const initialState = {
  events: [],
  currentEvent: null,
  status: 'idle',
  error: null,
  adminStatistics: null,
  adminStatisticsStatus: 'idle',
  adminStatisticsError: null,
};

export const fetchEvents = createAsyncThunk('events/fetchEvents', async () => {
  try {
    return await apiService.getEvents();
  } catch (error) {
    throw new Error('Failed to fetch events');
  }
});

export const fetchEvent = createAsyncThunk('events/fetchEvent', async (eventId) => {
  try {
    return await apiService.getEvent(eventId);
  } catch (error) {
    throw new Error('Failed to fetch event');
  }
});

export const createEventWithImage = createAsyncThunk('events/createEventWithImage', async (formData) => {
  try {
    return await apiService.createEventWithImage(formData);
  } catch (error) {
    throw new Error('Failed to create event');
  }
});

export const updateEvent = createAsyncThunk('events/updateEvent', async ({ eventId, eventData }) => {
  try {
    return await apiService.updateEvent(eventId, eventData);
  } catch (error) {
    throw new Error('Failed to update event');
  }
});

export const deleteEvent = createAsyncThunk('events/deleteEvent', async (eventId) => {
  try {
    return await apiService.deleteEvent(eventId);
  } catch (error) {
    throw new Error('Failed to delete event');
  }
});

export const fetchAdminStatistics = createAsyncThunk('admin/fetchStatistics', async () => {
  try {
    return await apiService.getAdminStatistics();
  } catch (error) {
    throw new Error('Failed to fetch admin statistics');
  }
});

const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    setCurrentEvent: (state, action) => {
      state.currentEvent = action.payload;
    },
    clearCurrentEvent: (state) => {
      state.currentEvent = null;
    },
    clearEvents: (state) => {
      state.events = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchEvents.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchEvents.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.events = action.payload;
        state.error = null;
      })
      .addCase(fetchEvents.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(fetchEvent.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchEvent.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.currentEvent = action.payload;
        state.error = null;
      })
      .addCase(fetchEvent.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(createEventWithImage.fulfilled, (state, action) => {
        state.events.push(action.payload);
        state.error = null;
      })
      .addCase(fetchAdminStatistics.pending, (state) => {
        state.adminStatisticsStatus = 'loading';
        state.adminStatisticsError = null;
      })
      .addCase(fetchAdminStatistics.fulfilled, (state, action) => {
        state.adminStatisticsStatus = 'succeeded';
        state.adminStatistics = action.payload;
        state.adminStatisticsError = null;
      })
      .addCase(fetchAdminStatistics.rejected, (state, action) => {
        state.adminStatisticsStatus = 'failed';
        state.adminStatisticsError = action.error.message;
      });
  },
});

export const { setCurrentEvent, clearCurrentEvent, clearEvents } = eventsSlice.actions;

export const selectEvents = (state) => state.events.events;
export const selectCurrentEvent = (state) => state.events.currentEvent;
export const selectEventsStatus = (state) => state.events.status;
export const selectEventsError = (state) => state.events.error;
export const selectAdminStatistics = (state) => state.events.adminStatistics;
export const selectAdminStatisticsStatus = (state) => state.events.adminStatisticsStatus;
export const selectAdminStatisticsError = (state) => state.events.adminStatisticsError;

export default eventsSlice.reducer; 