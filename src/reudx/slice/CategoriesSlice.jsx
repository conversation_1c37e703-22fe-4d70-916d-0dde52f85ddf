import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import apiService from '../../services/api';

const initialState = {
  categories: [],
  currentCategory: null,
  status: 'idle',
  error: null,
};

export const fetchCategories = createAsyncThunk('categories/fetchCategories', async (eventId) => {
  try {
    return await apiService.getCategories(eventId);
  } catch (error) {
    throw new Error('Failed to fetch categories');
  }
});

export const createCategory = createAsyncThunk('categories/createCategory', async ({ eventId, categoryData }) => {
  try {
    return await apiService.createCategory(eventId, categoryData);
  } catch (error) {
    throw new Error('Failed to create category');
  }
});

export const updateCategory = createAsyncThunk('categories/updateCategory', async ({ eventId, categoryId, categoryData }) => {
  try {
    return await apiService.updateCategory(eventId, categoryId, categoryData);
  } catch (error) {
    throw new Error('Failed to update category');
  }
});

export const deleteCategory = createAsyncThunk('categories/deleteCategory', async ({ eventId, categoryId }) => {
  try {
    return await apiService.deleteCategory(eventId, categoryId);
  } catch (error) {
    throw new Error('Failed to delete category');
  }
});

const categoriesSlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    setCurrentCategory: (state, action) => {
      state.currentCategory = action.payload;
    },
    clearCurrentCategory: (state) => {
      state.currentCategory = null;
    },
    clearCategories: (state) => {
      state.categories = [];
    },
    addCategory: (state, action) => {
      state.categories.push(action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCategories.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.categories = action.payload;
        state.error = null;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(createCategory.fulfilled, (state, action) => {
        state.categories.push(action.payload);
        state.error = null;
      });
  },
});

export const { setCurrentCategory, clearCurrentCategory, clearCategories, addCategory } = categoriesSlice.actions;

export const selectCategories = (state) => state.categories.categories;
export const selectCurrentCategory = (state) => state.categories.currentCategory;
export const selectCategoriesStatus = (state) => state.categories.status;
export const selectCategoriesError = (state) => state.categories.error;

export default categoriesSlice.reducer; 