import { configureStore } from '@reduxjs/toolkit';
import nomineesReducer from './slice/NomineesSlice';
import eventsReducer from './slice/EventsSlice';
import categoriesReducer from './slice/CategoriesSlice';
import authReducer from './slice/AuthSlice';

const store = configureStore({
  reducer: {
    nominees: nomineesReducer,
    events: eventsReducer,
    categories: categoriesReducer,
    auth: authReducer,
  },
});

export default store;
