import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTrophy, FaArrowLeft, FaUsers, FaMoneyBillWave } from 'react-icons/fa';
import { fetchEvent, selectCurrentEvent } from '../reudx/slice/EventsSlice';
import { fetchNomineesByCategory, selectNominees, selectNomineesStatus, selectNomineesError, incrementVotes, updateNominee, deleteNominee } from '../reudx/slice/NomineesSlice';
import VoteForm from './VoteForm';
import LoadingSpinner from './LoadingSpinner';
import { selectUser } from '../reudx/slice/AuthSlice';
import { updateCategory, deleteCategory } from '../reudx/slice/CategoriesSlice';

const NomineeCard = ({ nominee, event, onVote }) => {
  const user = useSelector(selectUser);
  const isAdmin = user && user.role === 'admin';
  const isNomineeOwner = user && (isAdmin || nominee.user_id === user.id);
  const navigate = useNavigate();

  const handleEditNominee = (nomineeId) => {
    alert('Edit nominee form/modal coming soon!');
  };
  const handleDeleteNominee = async (nomineeId) => {
    if (window.confirm('Are you sure you want to delete this nominee?')) {
      try {
        await dispatch(deleteNominee({ eventId, categoryId, nomineeId })).unwrap();
        dispatch(fetchNomineesByCategory({ eventId, categoryId }));
      } catch (err) {
        alert('Failed to delete nominee.');
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="nominee-card"
    >
      <div className="nominee-image-wrapper">
        {nominee.image_url ? (
          <img
            src={nominee.image_url}
            alt={nominee.name}
            className="nominee-image"
          />
        ) : (
          <div className="nominee-image-placeholder">
            <FaUsers size={40} color="#6b7280" />
          </div>
        )}
      </div>
      <div className="nominee-info">
        <h3 className="nominee-name">{nominee.name}</h3>
        <p className="nominee-code">Code: {nominee.nominee_code}</p>
      </div>
      <div className="nominee-voting">
        <VoteForm 
          nomineeId={nominee.id} 
          handleVote={onVote}
          votingPrice={event?.voting_price}
        />
      </div>
      {/* Edit/Delete for Nominee */}
      {isNomineeOwner && (
        <div style={{ marginTop: 8 }}>
          <button className="btn btn-primary btn-xs" onClick={() => handleEditNominee(nominee.id)} style={{ marginRight: 6 }}>
            Edit
          </button>
          <button className="btn btn-danger btn-xs" onClick={() => handleDeleteNominee(nominee.id)}>
            Delete
          </button>
        </div>
      )}
    </motion.div>
  );
};

const CategoryDetail = () => {
  const { eventId, categoryId } = useParams();
  const dispatch = useDispatch();
  const event = useSelector(selectCurrentEvent);
  const nominees = useSelector(selectNominees);
  const status = useSelector(selectNomineesStatus);
  const error = useSelector(selectNomineesError);
  const user = useSelector(selectUser);
  const isAdmin = user && user.role === 'admin';
  const navigate = useNavigate();
  // Find category info if available
  const category = event && event.categories ? event.categories.find(cat => cat.id === Number(categoryId)) : null;
  const isCategoryOwner = user && category && (isAdmin || category.user_id === user.id);

  useEffect(() => {
    if (categoryId && eventId) {
      dispatch(fetchNomineesByCategory({ eventId, categoryId }));
    }
    if (eventId && !event) {
      dispatch(fetchEvent(eventId));
    }
  }, [dispatch, categoryId, eventId, event]);

  const handleVote = (nomineeId, voteCount) => {
    dispatch(incrementVotes({ nomineeId, incrementBy: voteCount }));
  };

  const handleEditCategory = () => {
    alert('Edit category form/modal coming soon!');
  };
  const handleDeleteCategory = async () => {
    if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      try {
        await dispatch(deleteCategory({ eventId, categoryId })).unwrap();
        navigate(`/events/${eventId}`);
      } catch (err) {
        alert('Failed to delete category.');
      }
    }
  };

  if (status === 'loading') {
    return <LoadingSpinner message="Loading nominees..." type="nominees" />;
  }

  if (status === 'failed') {
    return (
      <div className="error-container">
        <h2>Error Loading Nominees</h2>
        <p>{error}</p>
        <Link to={`/events/${eventId}`} className="btn btn-primary">
          Back to Event
        </Link>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <Link to="/" className="logo">
            <FaTrophy size={24} color="#818cf8" />
            EaseVote
          </Link>
          <nav>
            <Link to={`/events/${eventId}`} className="btn btn-secondary btn-sm">
              <FaArrowLeft size={16} />
              Back to Event
            </Link>
          </nav>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="main-container">
        <motion.div
          className="category-detail-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {/* Category Header */}
          <motion.div
            className="category-header"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="category-header-content">
              <div className="category-icon-section">
                <FaTrophy size={48} color="#f59e0b" />
              </div>
              <div className="category-header-info">
                <h1 className="category-detail-title">Category Nominees</h1>
                <p className="category-detail-subtitle">
                  Cast your votes for your favorite nominees
                </p>
                {event && (
                  <div className="voting-info">
                    <div className="voting-price-info">
                      <FaMoneyBillWave size={18} color="#10b981" />
                      <span>{event.voting_price} GHS per vote</span>
                    </div>
                    <div className="nominee-count-info">
                      <FaUsers size={18} color="#6b7280" />
                      <span>{nominees.length} nominees</span>
                    </div>
                  </div>
                )}
                {/* Edit/Delete for Category */}
                {isCategoryOwner && (
                  <div style={{ marginTop: 12 }}>
                    <button className="btn btn-primary btn-xs" onClick={handleEditCategory} style={{ marginRight: 6 }}>
                      Edit Category
                    </button>
                    <button className="btn btn-danger btn-xs" onClick={handleDeleteCategory}>
                      Delete Category
                    </button>
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Nominees Section */}
          <motion.div
            className="nominees-section"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="section-header">
              <h2>Nominees</h2>
              <p>Select a nominee and cast your vote</p>
            </div>

            {nominees.length > 0 ? (
              <div className="nominees-grid">
                <AnimatePresence>
                  {nominees.map((nominee, index) => (
                    <NomineeCard
                      key={nominee.id}
                      nominee={nominee}
                      event={event}
                      onVote={handleVote}
                      index={index}
                    />
                  ))}
                </AnimatePresence>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                style={{ textAlign: 'center', padding: '2rem' }}
              >
                <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                  No nominees available in this category yet.
                </p>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </main>
    </>
  );
};

export default CategoryDetail; 