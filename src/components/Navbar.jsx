import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectIsAuthenticated } from '../reudx/slice/AuthSlice';
import UserMenu from './auth/UserMenu';
import { FaBars, FaTimes } from 'react-icons/fa';

const navItems = [
  { label: 'Services', to: '/services' },
  { label: 'Contact', to: '/contact' },
];

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const navigate = useNavigate();

  const handleLogin = () => {
    navigate('/auth');
    setMenuOpen(false);
  };

  const handleSignUp = () => {
    navigate('/register');
    setMenuOpen(false);
  };

  return (
    <nav className="main-navbar">
      <div className="nav-brand" onClick={() => navigate('/')}>EaseVote</div>
      <div className="nav-desktop">
        {navItems.map((item) => (
          <Link key={item.to} to={item.to} className="nav-link">
            {item.label}
          </Link>
        ))}
        {!isAuthenticated && (
          <>
            <button className="nav-link nav-login" onClick={handleLogin}>
              Login
            </button>
            <button className="nav-link nav-signup" onClick={handleSignUp}>
              Sign Up
            </button>
          </>
        )}
        {isAuthenticated && <UserMenu />}
      </div>
      <div className="nav-mobile">
        <button
          className="nav-hamburger"
          onClick={() => setMenuOpen((open) => !open)}
          aria-label="Open menu"
        >
          {menuOpen ? <FaTimes size={22} /> : <FaBars size={22} />}
        </button>
        {menuOpen && (
          <div className="nav-dropdown">
            {navItems.map((item) => (
              <Link
                key={item.to}
                to={item.to}
                className="dropdown-link"
                onClick={() => setMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}
            {!isAuthenticated && (
              <>
                <button className="dropdown-link nav-login" onClick={handleLogin}>
                  Login
                </button>
                <button className="dropdown-link nav-signup" onClick={handleSignUp}>
                  Sign Up
                </button>
              </>
            )}
            {isAuthenticated && (
              <li className="dropdown-link">
                <Link to="/my-submissions">My Submissions</Link>
              </li>
            )}
            {isAuthenticated && <div className="dropdown-user-menu"><UserMenu /></div>}
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar; 