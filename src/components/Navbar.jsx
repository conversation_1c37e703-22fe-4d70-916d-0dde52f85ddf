import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { selectIsAuthenticated } from '../reudx/slice/AuthSlice';
import UserMenu from './auth/UserMenu';
import {
  FaBars,
  FaTimes,
  FaTrophy,
  FaPlus,
  FaChartBar,
  FaHome,
  FaServicestack,
  FaEnvelope
} from 'react-icons/fa';

const navItems = [
  { label: 'Home', to: '/', icon: FaHome },
  { label: 'Results', to: '/results', icon: FaChartBar },
  { label: 'Services', to: '/services', icon: FaServicestack },
  { label: 'Contact', to: '/contact', icon: FaEnvelope },
];

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const navigate = useNavigate();

  const handleLogin = () => {
    navigate('/auth');
    setMenuOpen(false);
  };

  const handleSignUp = () => {
    navigate('/register');
    setMenuOpen(false);
  };

  const closeMenu = () => {
    setMenuOpen(false);
  };

  return (
    <motion.nav
      className="modern-navbar"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="navbar-container">
        {/* Brand Logo */}
        <motion.div
          className="navbar-brand"
          onClick={() => navigate('/')}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaTrophy className="brand-icon" />
          <span className="brand-text">EaseVote</span>
        </motion.div>

        {/* Desktop Navigation */}
        <div className="navbar-desktop">
          <div className="nav-links">
            {navItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div
                  key={item.to}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Link to={item.to} className="nav-link">
                    <IconComponent className="nav-icon" />
                    <span>{item.label}</span>
                  </Link>
                </motion.div>
              );
            })}

            {/* Create Event Link for Authenticated Users */}
            {isAuthenticated && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Link to="/admin/create-event" className="nav-link create-event">
                  <FaPlus className="nav-icon" />
                  <span>Create Event</span>
                </Link>
              </motion.div>
            )}
          </div>

          {/* Auth Section */}
          <div className="navbar-auth">
            {!isAuthenticated ? (
              <div className="auth-buttons">
                <motion.button
                  className="auth-btn login-btn"
                  onClick={handleLogin}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Login
                </motion.button>
                <motion.button
                  className="auth-btn signup-btn"
                  onClick={handleSignUp}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Sign Up
                </motion.button>
              </div>
            ) : (
              <UserMenu />
            )}
          </div>
        </div>

        {/* Mobile Menu Toggle */}
        <div className="navbar-mobile">
          <motion.button
            className="mobile-menu-btn"
            onClick={() => setMenuOpen(!menuOpen)}
            whileTap={{ scale: 0.95 }}
            aria-label="Toggle menu"
          >
            <AnimatePresence mode="wait">
              {menuOpen ? (
                <motion.div
                  key="close"
                  initial={{ rotate: -90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: 90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <FaTimes size={24} />
                </motion.div>
              ) : (
                <motion.div
                  key="menu"
                  initial={{ rotate: 90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: -90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <FaBars size={24} />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.button>
        </div>
      </div>

      {/* Mobile Dropdown Menu */}
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            className="mobile-dropdown"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="mobile-menu-content">
              {navItems.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={item.to}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Link
                      to={item.to}
                      className="mobile-nav-link"
                      onClick={closeMenu}
                    >
                      <IconComponent className="mobile-nav-icon" />
                      <span>{item.label}</span>
                    </Link>
                  </motion.div>
                );
              })}

              {/* Create Event for Mobile */}
              {isAuthenticated && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                >
                  <Link
                    to="/admin/create-event"
                    className="mobile-nav-link create-event"
                    onClick={closeMenu}
                  >
                    <FaPlus className="mobile-nav-icon" />
                    <span>Create Event</span>
                  </Link>
                </motion.div>
              )}

              {/* Mobile Auth */}
              {!isAuthenticated && (
                <div className="mobile-auth">
                  <motion.button
                    className="mobile-auth-btn login"
                    onClick={handleLogin}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.5 }}
                  >
                    Login
                  </motion.button>
                  <motion.button
                    className="mobile-auth-btn signup"
                    onClick={handleSignUp}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.6 }}
                  >
                    Sign Up
                  </motion.button>
                </div>
              )}

              {isAuthenticated && (
                <motion.div
                  className="mobile-user-menu"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <UserMenu />
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navbar; 