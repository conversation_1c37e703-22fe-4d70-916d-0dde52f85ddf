import influencerIcon from '../../assets/icons/influencerIcon.png';
import djIcon from '../../assets/icons/djIcon.png';
import entrepreneurIcon from '../../assets/icons/entrepreneurIcon.png';
import radioStationIcon from '../../assets/icons/radioStationIcon.png';
import radioProgramIcon from '../../assets/icons/radioProgramIcon.png';
import hardworkingYouthIcon from '../../assets/icons/hardworkingYouthIcon.png';
import popularPersonalityIcon from '../../assets/icons/popularPersonalityIcon.png';
import radioPersonalityIcon from '../../assets/icons/radioPersonalityIcon.png';
import sportPersonalityIcon from '../../assets/icons/sportPersonalityIcon.png';
import femaleNewscasterIcon from '../../assets/icons/newscasterIcon.png';
import maleNewsCasterIcon from '../../assets/icons/maleNewscasterIcon.png';
import maleFashionDesignerIcon from '../../assets/icons/maleFashionDesignerIcon.png';
import femaleFashionDesignerIcon from '../../assets/icons/femaleFashionDesignerIcon.png';
import graphicDesignerIcon from '../../assets/icons/graphicDesignerIcon.png';
import photographerIcon from '../../assets/icons/photographerIcon.png';
import dancerIcon from '../../assets/icons/dancerIcon.png';
import danceGroupIcon from '../../assets/icons/danceGroupIcon.png';
import rapperIcon from '../../assets/icons/rapperIcon.png';
import producerIcon from '../../assets/icons/producerIcon.png';
import hiplifeArtistIcon from '../../assets/icons/hiplifeArtistIcon.png';
import dancehallArtistIcon from '../../assets/icons/dancehallArtistIcon.png';
import gospelArtistIcon from '../../assets/icons/gospelArtistIcon.png';
import vocalistIcon from '../../assets/icons/vocalistIcon.png';
import maleVocalistIcon from '../../assets/icons/maleVocalistIcon.png';
import artisteIcon from '../../assets/icons/artisteIcon.png';
import popularSongIcon from '../../assets/icons/popularSongIcon.png';
import musicVideoIcon from '../../assets/icons/musicVideoIcon.png';
import videoDirectorIcon from '../../assets/icons/videoDirectorIcon.png';
import socialMediaPersonalityIcon from '../../assets/icons/socialMediaPersonalityIcon.png';
import eventIcon from '../../assets/icons/eventIcon.png';
import makeupArtistIcon from '../../assets/icons/makeUpArtisteIcon.png';
import hairStylistIcon from '../../assets/icons/hairStylistIcon.png';
import barberIcon from '../../assets/icons/barberIcon.png';
import keepfitClubIcon from '../../assets/icons/keepfitClubIcon.png';
import mcIcon from '../../assets/icons/mcIcon.png';
import restaurantIcon from '../../assets/icons/restaurantIcon.png';
import herbalClinicIcon from '../../assets/icons/herbalClinicIcon.png';
import radioPoliticalShowIcon from '../../assets/icons/radioPoliticalShowIcon.png';
import radioPoliticalShowHostIcon from '../../assets/icons/radioPoliticalShowHostIcon.png';
import youthPoliticalAdvocateIcon from '../../assets/icons/youthPoliticalAdvocateIcon.png';
import reggaeDancehallSong from '../../assets/icons/reggaeDancehallSongIcon.png';
import radioPresenter from '../../assets/icons/radioPresenterIcon.png';
import radioSportHost from '../../assets/icons/radioSportsHostIcon.png';
import RadioSportsForeignNewsPresenter from '../../assets/icons/radioSportsForeignNewsPresenterIcon.png';
import RadioSportsAnalyst from '../../assets/icons/radioSportsAnalystIcon.png';
import RadioSportsProgram from '../../assets/icons/radioSportsProgramIcon.png';
import RadioEntertainmentShow from '../../assets/icons/radioEntertainmentShowIcon.png';
import EventDecorator from '../../assets/icons/eventDecoratorIcon.png';
import NewArtist from '../../assets/icons/newArtistIcon.png';
import MusicGroup from '../../assets/icons/musicGroupIcon.png';
import hiplifSong from '../../assets/icons/hiplifSongIcon.png';

const categories = [
  {
    title: 'Influencer of The Year',
    link: '/influencer',
    icon: influencerIcon,
    alt: 'Influencer icon',
  },
  {
    title: 'DJ of The Year',
    link: '/djs',
    icon: djIcon,
    alt: 'DJ icon',
  },
  {
    title: 'Youth Entrepreneur of The Year',
    link: '/youthentrepreneur',
    icon: entrepreneurIcon,
    alt: 'Youth Entrepreneur icon',
  },
  {
    title: 'Radio Station of The Year',
    link: '/radiostation',
    icon: radioStationIcon,
    alt: 'Radio Station icon',
  },
  {
    title: 'Radio Program of The Year',
    link: '/radioprogram',
    icon: radioProgramIcon,
    alt: 'Radio Program icon',
  },
  {
    title: 'Hardworking Youth of The Year',
    link: '/hardworkingyouth',
    icon: hardworkingYouthIcon,
    alt: 'Hardworking Youth icon',
  },
  {
    title: 'Most Popular Personality of The Year',
    link: '/mostpopularpersonality',
    icon: popularPersonalityIcon,
    alt: 'Most Popular Personality icon',
  },
  {
    title: 'Radio Personality of The Year',
    link: '/radiopersonality',
    icon: radioPersonalityIcon,
    alt: 'Radio Personality icon',
  },
  {
    title: 'Radio Sport Personality of The Year',
    link: '/radiosportpersonality',
    icon: sportPersonalityIcon,
    alt: 'Radio Sport Personality icon',
  },
  {
    title: 'Male Newscaster of The Year ',
    link: '/malenewscaster',
    icon: maleNewsCasterIcon,
    alt: 'Newscaster icon',
  },
  {
    title: 'Female Newscaster of The Year ',
    link: '/femalenewscaster',
    icon: femaleNewscasterIcon,
    alt: 'Newscaster icon',
  },
  {
    title: 'Male Fashion Designer of The Year',
    link: '/malefashiondesigner',
    icon: maleFashionDesignerIcon,
    alt: 'Male Fashion Designer icon',
  },
  {
    title: 'Female Fashion Designer of The Year',
    link: '/femalefashiondesigner',
    icon: femaleFashionDesignerIcon,
    alt: 'Female Fashion Designer icon',
  },
  {
    title: 'Graphic Designer of The Year',
    link: '/graphicdesigner',
    icon: graphicDesignerIcon,
    alt: 'Graphic Designer icon',
  },
  {
    title: 'Photographer of The Year',
    link: '/photographer',
    icon: photographerIcon,
    alt: 'Photographer icon',
  },
  {
    title: 'Dancer of The Year',
    link: '/dancer',
    icon: dancerIcon,
    alt: 'Dancer icon',
  },
  {
    title: 'Dance Group of The Year',
    link: '/dancegroup',
    icon: danceGroupIcon,
    alt: 'Dance Group icon',
  },
  {
    title: 'Rapper of The Year',
    link: '/rapper',
    icon: rapperIcon,
    alt: 'Rapper icon',
  },
  {
    title: 'Producer of The Year',
    link: '/producer',
    icon: producerIcon,
    alt: 'Producer icon',
  },
  {
    title: 'Hiplife HipHop Artist of The Year',
    link: '/hiplifehiphop',
    icon: hiplifeArtistIcon,
    alt: 'Hiplife Artist icon',
  },
  {
    title: 'Dancehall Artist of The Year',
    link: '/dancehallartist',
    icon: dancehallArtistIcon,
    alt: 'Dancehall Artist Icon',
  },
  {
    title: 'Gospel Artist of The Year',
    link: '/gospelartist',
    icon: gospelArtistIcon,
    alt: 'Gospel Artist Icon',
  },
  {
    title: 'Female Vocalist of The Year',
    link: '/femalevocalist',
    icon: vocalistIcon,
    alt: 'Vocalist Icon',
  },
  {
    title: 'Male Vocalist of The Year',
    link: '/malevocalist',
    icon: maleVocalistIcon,
    alt: 'Vocalist Icon',
  },
  {
    title: 'Artiste of The Year',
    link: '/artiste',
    icon: artisteIcon,
    alt: 'Artiste Icon',
  },
  {
    title: 'Popular Song of The Year',
    link: '/popularsong',
    icon: popularSongIcon,
    alt: 'Popular Song Icon',
  },
  {
    title: 'Music Video of The Year',
    link: '/musicvideo',
    icon: musicVideoIcon,
    alt: 'Music Video Icon',
  },
  {
    title: 'Video Director of The Year',
    link: '/videodirector',
    icon: videoDirectorIcon,
    alt: 'Video Director Icon',
  },
  {
    title: 'Social Media Personality of The Year',
    link: '/socialmediapersonality',
    icon: socialMediaPersonalityIcon,
    alt: 'Social Media Personality Icon',
  },
  {
    title: 'Event of The Year',
    link: '/event',
    icon: eventIcon,
    alt: 'Event Icon',
  },
  {
    title: 'MakeUp Artist of The Year',
    link: '/makeupartiste',
    icon: makeupArtistIcon,
    alt: 'MakeUp Artiste Icon',
  },
  {
    title: 'Hair Stylist of The Year',
    link: '/hairstylist',
    icon: hairStylistIcon,
    alt: 'Hair Stylist Icon',
  },
  {
    title: 'Barber of The Year',
    link: '/barber',
    icon: barberIcon,
    alt: 'Barber Icon',
  },
  {
    title: 'Keepfit Club of The Year',
    link: '/keepfitclub',
    icon: keepfitClubIcon,
    alt: 'Keepfit Club Icon',
  },
  {
    title: 'MC of The Year',
    link: '/mc',
    icon: mcIcon,
    alt: 'MC Icon',
  },
  {
    title: 'Restaurant of The Year',
    link: '/restaurant',
    icon: restaurantIcon,
    alt: 'Restaurant Icon',
  },
  {
    title: 'Herbal Clinic of The Year',
    link: '/herbalclinic',
    icon: herbalClinicIcon,
    alt: 'Herbal Clinic Icon',
  },
  {
    title: 'Radio Political Show of The Year',
    link: '/radiopoliticalshow',
    icon: radioPoliticalShowIcon,
    alt: 'Radio Political Show Icon',
  },
  {
    title: 'Radio Political Show Host of The Year',
    link: '/radiopoliticalshowhost',
    icon: radioPoliticalShowHostIcon,
    alt: 'Radio Political Show Host Icon',
  },
  {
    title: 'Youth Political Advocate of The Year',
    link: '/youthpoliticaladvocate',
    icon: youthPoliticalAdvocateIcon,
    alt: 'Youth Political Advocate Icon',
  },
  {
    title: 'Reggae Dancehall Song of The Year',
    link: '/reggaedancehallsong',
    icon: reggaeDancehallSong,
    alt: 'Reggae Dancehall Song Icon',
  },
  {
    title: 'Radio Presenter of The Year',
    link: '/radiopresenter',
    icon: radioPresenter,
    alt: 'Radio Presenter Icon',
  },
  {
    title: 'Radio Sports Host of The Year',
    link: '/radiosporthost',
    icon: radioSportHost,
    alt: 'Radio Sports Host Icon',
  },
  {
    title: 'Radio Sports Foreign News Presenter of The Year',
    link: '/radiosportsforeignnewspresenter',
    icon: RadioSportsForeignNewsPresenter,
    alt: 'Radio Sports Foreign News Presenter Icon',
  },
  {
    title: 'Radio Sports Analyst of The Year',
    link: '/radiosportsanalyst',
    icon: RadioSportsAnalyst,
    alt: 'Radio Sports Analyst Icon',
  },
  {
    title: 'Radio Sports Program of The Year',
    link: '/radiosportspogram',
    icon: RadioSportsProgram,
    alt: 'Radio Sports Program Icon',
  },
  {
    title: 'Radio Entertainment Show of The Year',
    link: '/radioentertainmentshow',
    icon: RadioEntertainmentShow,
    alt: 'Radio Entertainment Show Icon',
  },
  {
    title: 'Event Decorator of The Year',
    link: '/eventdecorator',
    icon: EventDecorator,
    alt: 'Event Decorator Icon',
  },
  {
    title: 'New Artist of The Year',
    link: '/newartist',
    icon: NewArtist,
    alt: 'New Artist Icon',
  },
  {
    title: 'Music Group of The Year',
    link: '/musicgroup',
    icon: MusicGroup,
    alt: 'Music Group Icon',
  },
  {
    title: 'Hiplife HipHop Song of The Year',
    link: '/hiphophiplifesong',
    icon: hiplifSong,
    alt: 'Hiplife Song icon',
  },
];

export default categories;
