import { useState } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import { motion, AnimatePresence } from 'framer-motion';
import { FaVoteYea, FaCreditCard, FaCalculator, FaTimes, FaCheckCircle, FaCoins } from 'react-icons/fa';
import axios from 'axios';

const VoteForm = ({ nomineeId, handleVote, votingPrice = 1.0 }) => {
  const [amount, setAmount] = useState('');
  const [formVisible, setFormVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: amount, 2: confirmation

  const toggleFormVisibility = () => {
    setFormVisible(!formVisible);
    setStep(1);
    setAmount('');
  };

  const initiatePayment = async () => {
    setIsLoading(true);
    try {
      const response = await axios.post('https://voteserver.easevote.org/payments/initiate', {
        nominee_id: nomineeId,
        amount: parseInt(amount, 10),
      });

      const { authorization_url: authorizationUrl } = response.data;

      toast.success('Redirecting to payment gateway...', {
        position: 'bottom-right',
        autoClose: 2000,
      });

      setTimeout(() => {
        window.location.href = authorizationUrl;
      }, 1000);

    } catch (error) {
      toast.error('Payment initiation failed. Please try again.', {
        position: 'bottom-right',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (amount && parseInt(amount, 10) > 0) {
      if (step === 1) {
        setStep(2);
      } else {
        // Use the new handleVote function if provided, otherwise use payment
        if (handleVote) {
          handleVote(nomineeId, parseInt(amount, 10));
          toast.success('Vote cast successfully!', {
            position: 'bottom-right',
          });
          toggleFormVisibility();
        } else {
          initiatePayment();
        }
      }
    } else {
      toast.error(`Please enter a valid amount (minimum GH₵${votingPrice}).`, {
        position: 'bottom-right',
      });
    }
  };

  const handleBack = () => {
    setStep(1);
  };

  const numberOfVotes = parseInt(amount, 10) || 0;
  const totalCost = numberOfVotes * parseFloat(votingPrice);

  return (
    <div className="vote-form-container">
      <AnimatePresence mode="wait">
        {!formVisible ? (
          <motion.button
            key="vote-button"
            className="vote-trigger-btn"
            type="button"
            onClick={toggleFormVisibility}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <FaVoteYea className="vote-icon" />
            <span>Vote Now</span>
          </motion.button>
        ) : (
          <motion.div
            key="vote-form"
            className="vote-form-modal"
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <div className="vote-form-header">
              <h3 className="vote-form-title">
                <FaVoteYea className="title-icon" />
                Cast Your Vote
              </h3>
              <button
                className="close-btn"
                type="button"
                onClick={toggleFormVisibility}
                aria-label="Close"
              >
                <FaTimes />
              </button>
            </div>

            <div className="vote-form-content">
              <AnimatePresence mode="wait">
                {step === 1 ? (
                  <motion.div
                    key="step1"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="step-indicator">
                      <div className="step active">1</div>
                      <div className="step-line"></div>
                      <div className="step">2</div>
                    </div>

                    <div className="vote-info-card">
                      <FaCoins className="info-icon" />
                      <p className="vote-info">Each vote costs <strong>GH₵{votingPrice}</strong></p>
                      <p className="vote-subtitle">Enter the number of votes you'd like to cast</p>
                    </div>

                    <form onSubmit={handleSubmit} className="vote-form">
                      <div className="form-group">
                        <label className="form-label">
                          <FaCalculator className="label-icon" />
                          Number of Votes
                        </label>
                        <input
                          type="number"
                          placeholder="Enter number of votes"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          min="1"
                          required
                          className="form-input amount-input"
                          autoFocus
                        />
                      </div>

                      <div className="form-group">
                        <label className="form-label">
                          <FaCreditCard className="label-icon" />
                          Total Cost
                        </label>
                        <div className="votes-display">
                          <span className="votes-number">GH₵{totalCost.toFixed(2)}</span>
                          <span className="votes-text">({numberOfVotes} votes × GH₵{votingPrice})</span>
                        </div>
                      </div>

                      <button
                        type="submit"
                        className="btn btn-primary btn-lg vote-continue-btn"
                        disabled={!amount || parseInt(amount, 10) < 1}
                      >
                        Continue
                        <FaCheckCircle className="btn-icon" />
                      </button>
                    </form>
                  </motion.div>
                ) : (
                  <motion.div
                    key="step2"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="step-indicator">
                      <div className="step completed">✓</div>
                      <div className="step-line completed"></div>
                      <div className="step active">2</div>
                    </div>

                    <div className="confirmation-card">
                      <h4 className="confirmation-title">Confirm Your Vote</h4>

                      <div className="vote-summary">
                        <div className="summary-row">
                          <span className="summary-label">Number of Votes:</span>
                          <span className="summary-value">{numberOfVotes} votes</span>
                        </div>
                        <div className="summary-row">
                          <span className="summary-label">Price per Vote:</span>
                          <span className="summary-value">GH₵{votingPrice}</span>
                        </div>
                        <div className="summary-row total">
                          <span className="summary-label">Total Cost:</span>
                          <span className="summary-value">GH₵{totalCost.toFixed(2)}</span>
                        </div>
                      </div>

                      <div className="confirmation-actions">
                        <button
                          type="button"
                          onClick={handleBack}
                          className="btn btn-secondary"
                        >
                          Back
                        </button>
                        <button
                          type="button"
                          onClick={handleSubmit}
                          disabled={isLoading}
                          className="btn btn-success btn-lg"
                        >
                          {isLoading ? (
                            <>
                              <div className="loading-spinner-small"></div>
                              Processing...
                            </>
                          ) : (
                            <>
                              <FaVoteYea className="btn-icon" />
                              {handleVote ? 'Cast Vote' : 'Pay Now'}
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

VoteForm.propTypes = {
  nomineeId: PropTypes.number.isRequired,
  handleVote: PropTypes.func,
  votingPrice: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

export default VoteForm;
