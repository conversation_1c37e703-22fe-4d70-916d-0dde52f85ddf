import { useState } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import axios from 'axios';

const VoteForm = ({ nomineeId }) => {
  const [amount, setAmount] = useState('');
  const [formVisible, setFormVisible] = useState(false);

  const toggleFormVisibility = () => {
    setFormVisible(!formVisible);
  };

  const initiatePayment = async () => {
    try {
      const response = await axios.post('https://server1.easevote.org/payments/initiate', {
        nominee_id: nomineeId,
        amount: parseInt(amount, 10),
      });

      const { authorization_url: authorizationUrl } = response.data;
      window.location.href = authorizationUrl;
    } catch (error) {
      toast.error('Payment initiation failed. Please try again.', {
        position: toast.POSITION.BOTTOM_RIGHT,
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (amount) {
      initiatePayment();
    } else {
      toast.error('Please fill in both the amount and email fields.', {
        position: toast.POSITION.BOTTOM_RIGHT,
      });
    }
  };

  const numberOfVotes = parseInt(amount, 10) || 0;

  return (
    <div className="form-container">
      {formVisible ? (
        <div className="form-div">
          <button className="close-btn" type="button" onClick={toggleFormVisibility}>X</button>
          <form className="vote-form" onSubmit={handleSubmit}>
            <label htmlFor="amount">
              Each vote costs GH₵1. Enter the amount you&apos;d like to spend
              {' '}
              <input
                type="number"
                placeholder="Amount (GH₵)"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                min="1"
                required
                className="form-input"
              />
            </label>
            <div className="form-group">
              <label className="form-label">Number of Votes</label>
              <input
                type="number"
                placeholder={amount ? '' : 'Number of Votes'}
                value={numberOfVotes || ''}
                readOnly
                className="form-input"
              />
            </div>
            <button type="submit" className="btn btn-primary btn-lg">💳 Pay Now</button>
          </form>
        </div>
      ) : (
        <button className="btn btn-primary btn-lg" type="button" onClick={toggleFormVisibility}>
          🗳️ Vote Now
        </button>
      )}
    </div>
  );
};

VoteForm.propTypes = {
  nomineeId: PropTypes.number.isRequired,
};

export default VoteForm;
