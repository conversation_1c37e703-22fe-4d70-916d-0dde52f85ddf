import { motion } from 'framer-motion';
import {
  FaTrophy,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaFacebook,
  FaTwitter,
  FaInstagram,
  FaLinkedin,
} from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <motion.footer
      className="modern-footer"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="footer-container">
        {/* Main Footer Content */}
        <div className="footer-content">
          {/* Brand Section - Always First */}
          <div className="footer-section brand-section">
            <div className="footer-brand">
              <FaTrophy className="footer-brand-icon" />
              <span className="footer-brand-text">EaseVote</span>
            </div>
            <p className="footer-description">
              Making voting simple, secure, and accessible for everyone.
            </p>
            <div className="social-links">
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Facebook"
              >
                <FaFacebook />
              </motion.a>
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Twitter"
              >
                <FaTwitter />
              </motion.a>
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Instagram"
              >
                <FaInstagram />
              </motion.a>
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="LinkedIn"
              >
                <FaLinkedin />
              </motion.a>
            </div>
          </div>

          {/* Links Grid - Responsive Layout */}
          <div className="footer-links-grid">
            {/* Quick Links */}
            <div className="footer-section foot">
              <h3 className="footer-section-title">Quick Links</h3>
              <ul className="footer-links">
                <li><a href="/">Home</a></li>
                <li><a href="/results">Results</a></li>
                <li><a href="/services">Services</a></li>
                <li><a href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Support */}
            <div className="footer-section foot">
              <h3 className="footer-section-title">Support</h3>
              <ul className="footer-links">
                <li><a href="/help">Help Center</a></li>
                <li><a href="/privacy">Privacy Policy</a></li>
                <li><a href="/terms">Terms of Service</a></li>
                <li><a href="/faq">FAQ</a></li>
              </ul>
            </div>

            {/* Contact Info */}
            <div className="footer-section contact-section foot">
              <h3 className="footer-section-title">Contact Us</h3>
              <div className="contact-info">
                <motion.a
                  href="tel:+233240095360"
                  className="contact-item"
                  whileHover={{ x: 5 }}
                >
                  <FaPhone className="contact-icon" />
                  <span>+233 240 095 360</span>
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  className="contact-item"
                  whileHover={{ x: 5 }}
                >
                  <FaEnvelope className="contact-icon" />
                  <span><EMAIL></span>
                </motion.a>
                <div className="contact-item">
                  <FaMapMarkerAlt className="contact-icon" />
                  <span>Dunkwa Offin, Ghana</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p className="copyright">
              © {currentYear} Easing Life Group. All Rights Reserved.
            </p>
            <div className="footer-bottom-links">
              <a href="/privacy" className="footer-bottom-link">Privacy</a>
              <span className="separator">•</span>
              <a href="/terms" className="footer-bottom-link">Terms</a>
              <span className="separator">•</span>
              <a href="/cookies" className="footer-bottom-link">Cookies</a>
            </div>
          </div>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;
