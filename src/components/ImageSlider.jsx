import React from 'react';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import Navbar from './Navbar';
import easinglife from '../assets/easinglife.png';
import manu from '../assets/manu.png';
import ads from '../assets/ads.png';
import partners from '../assets/partners.png';

const ImageSlider = () => {
  return (
    <div className="header-container">
      <Navbar />
      <Carousel showArrows infiniteLoop autoPlay interval={4000}>
        <div>
          <img src={partners} alt="partner" />
        </div>
        <div>
          <img src={easinglife} alt="partner" />
        </div>
        <div>
          <img src={manu} alt="partner" />
        </div>
        <div>
          <img src={ads} alt="partner" />
        </div>
      </Carousel>
    </div>
  );
};

export default ImageSlider;
