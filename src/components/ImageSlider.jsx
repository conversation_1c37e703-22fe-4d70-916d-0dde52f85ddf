import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaTrophy, FaVoteYea, FaUsers, FaChartBar, FaArrowRight } from 'react-icons/fa';
import Navbar from './Navbar';
import partners from '../assets/partners.png';

const HeroSection = () => {
  return (
    <div className="hero-container">
      <Navbar />
      <section className="hero-section">
        <div className="hero-content">
          {/* Left Side - Text Content */}
          <motion.div
            className="hero-text"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <motion.h1
              className="hero-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <span className="hero-title-main">EaseVote</span>
              <span className="hero-title-sub">Voting Made Simple</span>
            </motion.h1>

            <motion.p
              className="hero-description"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              Experience the future of digital voting with our secure, transparent, and user-friendly platform.
              Cast your votes for your favorite nominees across multiple award categories with just a few clicks.
            </motion.p>

            <motion.div
              className="hero-features"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <div className="feature-item">
                <FaVoteYea className="feature-icon" />
                <span>Secure Voting</span>
              </div>
              <div className="feature-item">
                <FaUsers className="feature-icon" />
                <span>Multiple Categories</span>
              </div>
              <div className="feature-item">
                <FaChartBar className="feature-icon" />
                <span>Real-time Results</span>
              </div>
            </motion.div>

            <motion.div
              className="hero-actions"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <Link to="/" className="btn btn-primary btn-lg hero-cta">
                <FaTrophy className="btn-icon" />
                Start Voting Now
                <FaArrowRight className="btn-arrow" />
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Side - Image */}
          <motion.div
            className="hero-image"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="hero-image-container">
              <img src={partners} alt="EaseVote Platform" className="hero-main-image" />
              <div className="hero-image-overlay">
                <div className="floating-card card-1">
                  <FaTrophy className="card-icon" />
                  <div className="card-content">
                    <h4>Award Events</h4>
                    <p>Multiple categories</p>
                  </div>
                </div>
                <div className="floating-card card-2">
                  <FaVoteYea className="card-icon" />
                  <div className="card-content">
                    <h4>Easy Voting</h4>
                    <p>One-click process</p>
                  </div>
                </div>
                <div className="floating-card card-3">
                  <FaChartBar className="card-icon" />
                  <div className="card-content">
                    <h4>Live Results</h4>
                    <p>Real-time updates</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HeroSection;
