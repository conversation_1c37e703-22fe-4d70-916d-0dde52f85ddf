import React from 'react';

// Custom Trophy Icon
export const TrophyIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 21H16"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M10 21V19"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M14 21V19"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

// Custom Vote Icon
export const VoteIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="3"
      y="4"
      width="18"
      height="16"
      rx="2"
      stroke={color}
      strokeWidth="2"
      fill="none"
    />
    <path
      d="M7 12L10 15L17 8"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <circle
      cx="12"
      cy="2"
      r="1"
      fill={color}
    />
  </svg>
);

// Custom Award Icon
export const AwardIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="12"
      cy="8"
      r="6"
      stroke={color}
      strokeWidth="2"
      fill="none"
    />
    <path
      d="M15.477 12.89L17 22L12 19L7 22L8.523 12.89"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    <circle
      cx="12"
      cy="8"
      r="2"
      fill={color}
    />
  </svg>
);

// Custom Crown Icon
export const CrownIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2 18H22L20 8L16 12L12 6L8 12L4 8L2 18Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
      fillOpacity="0.1"
    />
    <circle cx="12" cy="6" r="1" fill={color} />
    <circle cx="8" cy="12" r="1" fill={color} />
    <circle cx="16" cy="12" r="1" fill={color} />
    <circle cx="4" cy="8" r="1" fill={color} />
    <circle cx="20" cy="8" r="1" fill={color} />
  </svg>
);

// Custom Medal Icon
export const MedalIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="12"
      cy="15"
      r="6"
      stroke={color}
      strokeWidth="2"
      fill="none"
    />
    <circle
      cx="12"
      cy="15"
      r="2"
      fill={color}
    />
    <path
      d="M8 3L10 9L12 3L14 9L16 3"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 9L12 9"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M12 9L16 9"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

// Custom Chart Icon
export const ChartIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="3"
      y="3"
      width="18"
      height="18"
      rx="2"
      stroke={color}
      strokeWidth="2"
      fill="none"
    />
    <rect
      x="7"
      y="12"
      width="3"
      height="6"
      fill={color}
      rx="1"
    />
    <rect
      x="14"
      y="8"
      width="3"
      height="10"
      fill={color}
      rx="1"
    />
    <rect
      x="10.5"
      y="15"
      width="3"
      height="3"
      fill={color}
      rx="1"
    />
  </svg>
);

// Custom Star Icon
export const StarIcon = ({ size = 24, color = "currentColor", filled = false, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill={filled ? color : "none"}
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Custom Ballot Icon
export const BallotIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4"
      y="2"
      width="16"
      height="20"
      rx="2"
      stroke={color}
      strokeWidth="2"
      fill="none"
    />
    <circle cx="8" cy="8" r="1" fill={color} />
    <path d="M11 8H17" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <circle cx="8" cy="12" r="1" fill={color} />
    <path d="M11 12H17" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <circle cx="8" cy="16" r="1" fill={color} />
    <path d="M11 16H17" stroke={color} strokeWidth="2" strokeLinecap="round" />
  </svg>
);

// Custom Results Icon
export const ResultsIcon = ({ size = 24, color = "currentColor", className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="3"
      y="3"
      width="18"
      height="18"
      rx="2"
      stroke={color}
      strokeWidth="2"
      fill="none"
    />
    <path
      d="M8 12L12 8L16 12"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 16L12 12L16 16"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default {
  TrophyIcon,
  VoteIcon,
  AwardIcon,
  CrownIcon,
  MedalIcon,
  ChartIcon,
  StarIcon,
  BallotIcon,
  ResultsIcon,
};
