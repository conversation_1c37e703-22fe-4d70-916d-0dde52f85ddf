import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTrophy, FaUsers, FaMoneyBillWave, FaArrowLeft, FaVoteYea } from 'react-icons/fa';
import { fetchEvent, selectCurrentEvent, selectEventsStatus, selectEventsError, updateEvent, deleteEvent } from '../reudx/slice/EventsSlice';
import { fetchCategories, selectCategories, selectCategoriesStatus } from '../reudx/slice/CategoriesSlice';
import LoadingSpinner from './LoadingSpinner';
import { selectUser } from '../reudx/slice/AuthSlice';

const CategoryCard = ({ category, eventId, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -8, scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
  >
    <Link to={`/events/${eventId}/categories/${category.id}`} className="category-card">
      <div className="category-icon-wrapper">
        <FaTrophy size={24} color="#f59e0b" />
      </div>
      <div className="category-info">
        <h3 className="category-title">{category.name}</h3>
        <p className="category-description">{category.description}</p>
        <div className="category-meta">
          <div className="nominee-count">
            <FaUsers size={16} color="#6b7280" />
            <span>{category.nominee_count || 0} nominees</span>
          </div>
        </div>
      </div>
      <div className="card-footer">
        <span className="vote-text">View Nominees</span>
        <div className="arrow-icon">→</div>
      </div>
    </Link>
  </motion.div>
);

const EventDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const event = useSelector(selectCurrentEvent);
  const eventStatus = useSelector(selectEventsStatus);
  const eventError = useSelector(selectEventsError);
  const categories = useSelector(selectCategories);
  const categoriesStatus = useSelector(selectCategoriesStatus);
  const user = useSelector(selectUser);
  const isOwner = user && event && (user.role === 'admin' || event.user_id === user.id);
  const isAdmin = user && user.role === 'admin';
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      dispatch(fetchEvent(id));
      dispatch(fetchCategories(id));
    }
  }, [dispatch, id]);

  const handleEdit = () => {
    alert('Edit event form/modal coming soon!');
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      try {
        await dispatch(deleteEvent(event.id)).unwrap();
        navigate('/');
      } catch (err) {
        alert('Failed to delete event.');
      }
    }
  };

  if (eventStatus === 'loading') {
    return <LoadingSpinner message="Loading event details..." type="event" />;
  }

  if (eventStatus === 'failed') {
    return (
      <div className="error-container">
        <h2>Error Loading Event</h2>
        <p>{eventError}</p>
        <Link to="/" className="btn btn-primary">
          Back to Events
        </Link>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="error-container">
        <h2>Event Not Found</h2>
        <p>The requested event could not be found.</p>
        <Link to="/" className="btn btn-primary">
          Back to Events
        </Link>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <Link to="/" className="logo">
            <FaTrophy size={24} color="#818cf8" />
            EaseVote
          </Link>
          <nav>
            <Link to="/" className="btn btn-secondary btn-sm">
              <FaArrowLeft size={16} />
              Back to Events
            </Link>
          </nav>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="main-container">
        <motion.div
          className="event-detail-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {/* Event Header */}
          <motion.div
            className="event-header"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="event-header-content">
              <div className="event-image-section">
                {event.image_url ? (
                  <img className="event-detail-image" src={event.image_url} alt={event.name} />
                ) : (
                  <div className="event-detail-image-placeholder">
                    <FaTrophy size={60} color="#818cf8" />
                  </div>
                )}
              </div>
              <div className="event-header-info">
                <h1 className="event-detail-title">{event.name}</h1>
                <p className="event-detail-description">{event.description}</p>
                <div className="event-detail-meta">
                  <div className="voting-price-detail">
                    <FaMoneyBillWave size={20} color="#10b981" />
                    <span className="price-text">{event.voting_price} GHS per vote</span>
                  </div>
                  {event.categories && (
                    <div className="category-count-detail">
                      <FaTrophy size={20} color="#f59e0b" />
                      <span>{event.categories.length} categories</span>
                    </div>
                  )}
                </div>
                {/* Edit/Delete Buttons */}
                {(isOwner || isAdmin) && (
                  <div style={{ marginTop: 16 }}>
                    {isOwner && (
                      <button className="btn btn-primary btn-sm" onClick={handleEdit} style={{ marginRight: 8 }}>
                        Edit Event
                      </button>
                    )}
                    {isAdmin && (
                      <button className="btn btn-danger btn-sm" onClick={handleDelete}>
                        Delete Event
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Categories Section */}
          <motion.div
            className="categories-section"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="section-header">
              <h2>Voting Categories</h2>
              <p>Choose a category to view nominees and cast your votes</p>
            </div>

            {categoriesStatus === 'loading' ? (
              <LoadingSpinner message="Loading categories..." type="categories" />
            ) : (
              <div className="categories-grid">
                <AnimatePresence>
                  {categories.map((category, index) => (
                    <CategoryCard
                      key={category.id}
                      category={category}
                      eventId={event.id}
                      index={index}
                    />
                  ))}
                </AnimatePresence>
              </div>
            )}

            {/* No Categories */}
            {categories.length === 0 && categoriesStatus === 'succeeded' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                style={{ textAlign: 'center', padding: '2rem' }}
              >
                <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                  No categories available for this event yet.
                </p>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </main>
    </>
  );
};

export default EventDetail; 