import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTrophy, FaUsers, FaMoneyBillWave, FaArrowLeft, FaVoteYea, FaArrowRight } from 'react-icons/fa';
import { fetchEvent, selectCurrentEvent, selectEventsStatus, selectEventsError, updateEvent, deleteEvent } from '../reudx/slice/EventsSlice';
import { fetchCategories, selectCategories, selectCategoriesStatus } from '../reudx/slice/CategoriesSlice';
import LoadingSpinner from './LoadingSpinner';
import { selectUser } from '../reudx/slice/AuthSlice';

const CategoryCard = ({ category, eventId, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -8, scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    className="category-card-wrapper"
  >
    <Link to={`/events/${eventId}/categories/${category.id}`} className="category-card enhanced">
      <div className="category-card-header">
        <div className="category-icon-section">
          <div className="category-icon-wrapper">
            <FaTrophy className="category-main-icon" />
          </div>
          <div className="category-overlay">
            <FaVoteYea className="overlay-icon" />
          </div>
        </div>
        <div className="category-badge">
          <span className="badge-dot"></span>
          <span className="badge-text">Active</span>
        </div>
      </div>

      <div className="category-content">
        <h3 className="category-title">{category.name}</h3>
        <p className="category-description">{category.description || "Vote for your favorite nominees in this category"}</p>

        <div className="category-stats">
          <div className="category-stat">
            <FaUsers className="stat-icon" />
            <span className="stat-text">{category.nominee_count || 0} nominees</span>
          </div>
        </div>
      </div>

      <div className="category-card-footer">
        <div className="footer-content">
          <span className="action-text">View Nominees</span>
          <div className="action-arrow">
            <FaArrowRight />
          </div>
        </div>
      </div>
    </Link>
  </motion.div>
);

const EventDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const event = useSelector(selectCurrentEvent);
  const eventStatus = useSelector(selectEventsStatus);
  const eventError = useSelector(selectEventsError);
  const categories = useSelector(selectCategories);
  const categoriesStatus = useSelector(selectCategoriesStatus);
  const user = useSelector(selectUser);
  const isOwner = user && event && (user.role === 'admin' || event.user_id === user.id);
  const isAdmin = user && user.role === 'admin';
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      dispatch(fetchEvent(id));
      dispatch(fetchCategories(id));
    }
  }, [dispatch, id]);

  const handleEdit = () => {
    alert('Edit event form/modal coming soon!');
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      try {
        await dispatch(deleteEvent(event.id)).unwrap();
        navigate('/');
      } catch (err) {
        alert('Failed to delete event.');
      }
    }
  };

  if (eventStatus === 'loading') {
    return <LoadingSpinner message="Loading event details..." type="event" />;
  }

  if (eventStatus === 'failed') {
    return (
      <div className="error-container">
        <h2>Error Loading Event</h2>
        <p>{eventError}</p>
        <Link to="/" className="btn btn-primary">
          Back to Events
        </Link>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="error-container">
        <h2>Event Not Found</h2>
        <p>The requested event could not be found.</p>
        <Link to="/" className="btn btn-primary">
          Back to Events
        </Link>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <Link to="/" className="logo">
            <FaTrophy size={24} color="#818cf8" />
            EaseVote
          </Link>
          <nav>
            <Link to="/" className="btn btn-secondary btn-sm">
              <FaArrowLeft size={16} />
              Back to Events
            </Link>
          </nav>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="main-container">
        <motion.div
          className="event-detail-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {/* Event Hero Section */}
          <motion.div
            className="event-hero"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="event-hero-content">
              <div className="event-hero-text">
                <div className="event-breadcrumb">
                  <Link to="/" className="breadcrumb-link">Events</Link>
                  <span className="breadcrumb-separator">→</span>
                  <span className="breadcrumb-current">{event.name}</span>
                </div>

                <h1 className="event-hero-title">{event.name}</h1>
                <p className="event-hero-description">{event.description}</p>

                <div className="event-hero-stats">
                  <div className="hero-stat-card">
                    <div className="stat-icon-wrapper">
                      <FaMoneyBillWave className="stat-icon" />
                    </div>
                    <div className="stat-info">
                      <span className="stat-label">Price per vote</span>
                      <span className="stat-value">GH₵{event.voting_price}</span>
                    </div>
                  </div>

                  <div className="hero-stat-card">
                    <div className="stat-icon-wrapper">
                      <FaTrophy className="stat-icon" />
                    </div>
                    <div className="stat-info">
                      <span className="stat-label">Categories</span>
                      <span className="stat-value">{categories.length}</span>
                    </div>
                  </div>

                  <div className="hero-stat-card">
                    <div className="stat-icon-wrapper">
                      <FaUsers className="stat-icon" />
                    </div>
                    <div className="stat-info">
                      <span className="stat-label">Status</span>
                      <span className="stat-value active">Active</span>
                    </div>
                  </div>
                </div>

                {(isOwner || isAdmin) && (
                  <div className="event-admin-actions">
                    {isOwner && (
                      <button onClick={handleEdit} className="btn btn-secondary">
                        Edit Event
                      </button>
                    )}
                    {isAdmin && (
                      <button onClick={handleDelete} className="btn btn-danger">
                        Delete Event
                      </button>
                    )}
                  </div>
                )}
              </div>

              <div className="event-hero-image">
                <div className="event-image-container">
                  {event.image_url ? (
                    <img className="event-detail-image" src={event.image_url} alt={event.name} />
                  ) : (
                    <div className="event-detail-image-placeholder">
                      <FaTrophy size={80} color="#818cf8" />
                    </div>
                  )}
                  <div className="image-overlay">
                    <div className="overlay-badge">
                      <span className="badge-text">Live Event</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Categories Section */}
          <motion.div
            className="categories-section"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="section-header">
              <h2>Voting Categories</h2>
              <p>Choose a category to view nominees and cast your votes</p>
            </div>

            {categoriesStatus === 'loading' ? (
              <LoadingSpinner message="Loading categories..." type="categories" />
            ) : (
              <div className="categories-grid">
                <AnimatePresence>
                  {categories.map((category, index) => (
                    <CategoryCard
                      key={category.id}
                      category={category}
                      eventId={event.id}
                      index={index}
                    />
                  ))}
                </AnimatePresence>
              </div>
            )}

            {/* No Categories */}
            {categories.length === 0 && categoriesStatus === 'succeeded' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                style={{ textAlign: 'center', padding: '2rem' }}
              >
                <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                  No categories available for this event yet.
                </p>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </main>
    </>
  );
};

export default EventDetail; 