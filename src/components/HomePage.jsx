import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSearch } from 'react-icons/fa';
import EndVote from './EndVote';
import LoadingSpinner from './LoadingSpinner';
import { TrophyIcon, BallotIcon } from './icons/CustomIcons';
import categories from './helpers/Categories';

const CategoryCard = ({
  title, to, icon, alt, index,
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -8, scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
  >
    <Link to={to} className="category-card">
      <div className="category-icon-wrapper">
        <img className="category-icon" src={icon} alt={alt} />
        <div className="icon-overlay">
          <BallotIcon size={20} color="white" />
        </div>
      </div>
      <h3 className="category-title">{title}</h3>
      <p className="category-description">Vote for your favorite in this category</p>
      <div className="card-footer">
        <span className="vote-text">Cast Vote</span>
        <div className="arrow-icon">→</div>
      </div>
    </Link>
  </motion.div>
);

const HomePageRenderer = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const filteredCategories = categories
    .filter((category) => category.title.toLowerCase().includes(searchQuery.toLowerCase()));

  if (isLoading) {
    return <LoadingSpinner message="Loading award categories..." type="awards" />;
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <motion.div
            className="logo"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <TrophyIcon size={24} color="#818cf8" />
            EaseVote
          </motion.div>
          <nav>
            <Link to="/results" className="btn btn-secondary btn-sm">
              View Results
            </Link>
          </nav>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="main-container">
        <motion.div
          className="home-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="home-title-container"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h1>Award Categories</h1>
            <h2>Choose a category to view nominees and cast your vote</h2>

            {/* Search Bar */}
            <motion.div
              className="search-container"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <FaSearch className="search-icon" />
              <input
                className="search-bar"
                type="text"
                placeholder="Search categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </motion.div>
          </motion.div>

          {/* Categories Grid */}
          <motion.div
            className="categories-grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <AnimatePresence>
              {filteredCategories.map(({
                title, link, icon, alt,
              }, index) => (
                <CategoryCard
                  key={title}
                  title={title}
                  to={link}
                  icon={icon}
                  alt={alt}
                  index={index}
                />
              ))}
            </AnimatePresence>
          </motion.div>

          {/* No Results */}
          <AnimatePresence>
            {filteredCategories.length === 0 && searchQuery && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                style={{ textAlign: 'center', padding: '2rem' }}
              >
                <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                  No categories found matching "{searchQuery}"
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </main>

      <EndVote />
    </>
  );
};

CategoryCard.propTypes = {
  title: PropTypes.string.isRequired,
  to: PropTypes.string.isRequired,
  icon: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
};

export default HomePageRenderer;
