import { useState } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import EndVote from './EndVote';
import categories from './helpers/Categories';

const CategoryCard = ({
  title, to, icon, alt,
}) => (
  <Link to={to} className="category-card">
    <img className="category-icon" src={icon} alt={alt} />
    <h3 className="category-title">{title}</h3>
    <p className="category-description">Vote for your favorite in this category</p>
  </Link>
);

const HomePageRenderer = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredCategories = categories
    .filter((category) => category.title.toLowerCase().includes(searchQuery.toLowerCase()));

  return (
    <>
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <div className="logo">🏆 EaseVote</div>
          <nav>
            <Link to="/outcome" className="btn btn-secondary btn-sm">
              View Results
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-container">
        <div className="home-container">
          <div className="home-title-container">
            <h1>Award Categories</h1>
            <h2>Choose a category to view nominees and cast your vote</h2>

            {/* Search Bar */}
            <div className="search-container">
              <div className="search-icon">🔍</div>
              <input
                className="search-bar"
                type="text"
                placeholder="Search categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Categories Grid */}
          <div className="categories-grid">
            {filteredCategories.map(({
              title, link, icon, alt,
            }) => (
              <CategoryCard
                key={title}
                title={title}
                to={link}
                icon={icon}
                alt={alt}
              />
            ))}
          </div>

          {/* No Results */}
          {filteredCategories.length === 0 && (
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                No categories found matching "{searchQuery}"
              </p>
            </div>
          )}
        </div>
      </main>

      <EndVote />
    </>
  );
};

CategoryCard.propTypes = {
  title: PropTypes.string.isRequired,
  to: PropTypes.string.isRequired,
  icon: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
};

export default HomePageRenderer;
