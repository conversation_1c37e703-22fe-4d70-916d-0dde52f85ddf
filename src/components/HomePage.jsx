import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import EndVote from './EndVote';
import categories from './helpers/Categories';

const HomePage = ({
  title, to, icon, alt,
}) => (
  <button type="button" className="links">
    <Link to={to}>
      <img className="icons-img" src={icon} alt={alt} />
      <h2>{title}</h2>
    </Link>
  </button>
);

const HomePageRenderer = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredCategories = categories
    .filter((category) => category.title.toLowerCase().includes(searchQuery.toLowerCase()));

  return (
    <>
      <div className="home-container">
        <div className="home-title-container">
          <h2>Categories</h2>
          <h3>Please select a category.</h3>
          <input
            className="search-bar"
            type="text"
            placeholder="Search by categories"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        {filteredCategories.map(({
          title, link, icon, alt,
        }) => (
          <HomePage
            key={title}
            title={title}
            to={link}
            icon={icon}
            alt={alt}
          />
        ))}
      </div>
      <EndVote />
    </>
  );
};

HomePage.propTypes = {
  title: PropTypes.string.isRequired,
  to: PropTypes.string.isRequired,
  icon: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
};

export default HomePageRenderer;
