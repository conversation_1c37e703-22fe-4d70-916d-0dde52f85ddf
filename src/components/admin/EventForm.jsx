import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import {
  FaCalendarAlt,
  FaImage,
  FaMoneyBillWave,
  FaFileAlt,
  FaSave,
  FaTimes,
  FaUpload,
  FaCheck
} from 'react-icons/fa';
import { createEventWithImage, updateEvent } from '../../reudx/slice/EventsSlice';
import { selectEventsStatus, selectEventsError } from '../../reudx/slice/EventsSlice';
import { useSelector as useReduxSelector } from 'react-redux';

const EventForm = ({ onSuccess, eventData = null, onCancel }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectEventsStatus);
  const error = useSelector(selectEventsError);
  const user = useReduxSelector(state => state.auth.user.data);

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [votingPrice, setVotingPrice] = useState('');
  const [image, setImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditMode = !!eventData;

  // Initialize form with event data if editing
  useEffect(() => {
    if (eventData) {
      setName(eventData.name || '');
      setDescription(eventData.description || '');
      setVotingPrice(eventData.voting_price || '');
      setImagePreview(eventData.image_url || null);
    }
  }, [eventData]);

  const handleImageChange = (file) => {
    if (file) {
      setImage(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileInput = (e) => {
    const file = e.target.files[0];
    handleImageChange(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleImageChange(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (isEditMode) {
        // Update existing event
        const updateData = {
          id: eventData.id,
          name,
          description,
          voting_price: votingPrice
        };

        if (image) {
          const formData = new FormData();
          formData.append('event[name]', name);
          formData.append('event[description]', description);
          formData.append('event[voting_price]', votingPrice);
          formData.append('event[image]', image);
          // Use image update endpoint if available
          const result = await dispatch(createEventWithImage(formData)).unwrap();
          onSuccess(result);
        } else {
          const result = await dispatch(updateEvent(updateData)).unwrap();
          onSuccess(result);
        }
      } else {
        // Create new event
        const formData = new FormData();
        formData.append('event[name]', name);
        formData.append('event[description]', description);
        formData.append('event[voting_price]', votingPrice);
        if (image) formData.append('event[image]', image);
        if (user?.id) formData.append('event[user_id]', user.id);

        const result = await dispatch(createEventWithImage(formData)).unwrap();
        onSuccess(result);

        // Reset form for create mode
        setName('');
        setDescription('');
        setVotingPrice('');
        setImage(null);
        setImagePreview(null);
      }
    } catch (err) {
      console.error(`Failed to ${isEditMode ? 'update' : 'create'} event:`, err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="modern-event-form-container"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="form-header">
        <div className="form-title-section">
          <FaCalendarAlt className="form-icon" />
          <div>
            <h2 className="form-title">
              {isEditMode ? 'Edit Event' : 'Create New Event'}
            </h2>
            <p className="form-subtitle">
              {isEditMode ? 'Update your event details' : 'Set up your voting event with all the details'}
            </p>
          </div>
        </div>
        {onCancel && (
          <motion.button
            type="button"
            className="close-btn"
            onClick={onCancel}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <FaTimes />
          </motion.button>
        )}
      </div>

      {error && (
        <motion.div
          className="error-alert"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
        >
          <span className="error-text">{error}</span>
        </motion.div>
      )}

      <form className="modern-event-form" onSubmit={handleSubmit} encType="multipart/form-data">
        <div className="form-grid">
          {/* Event Name */}
          <motion.div
            className="form-field"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <label className="field-label">
              <FaCalendarAlt className="label-icon" />
              Event Name
            </label>
            <input
              type="text"
              className="field-input"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Enter your event name..."
              required
            />
          </motion.div>

          {/* Voting Price */}
          <motion.div
            className="form-field"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <label className="field-label">
              <FaMoneyBillWave className="label-icon" />
              Voting Price (GHS)
            </label>
            <input
              type="number"
              className="field-input"
              min="0"
              step="0.01"
              value={votingPrice}
              onChange={e => setVotingPrice(e.target.value)}
              placeholder="0.00"
              required
            />
          </motion.div>
        </div>

        {/* Description */}
        <motion.div
          className="form-field full-width"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="field-label">
            <FaFileAlt className="label-icon" />
            Event Description
          </label>
          <textarea
            className="field-textarea"
            value={description}
            onChange={e => setDescription(e.target.value)}
            placeholder="Describe your event in detail..."
            rows={4}
            required
          />
        </motion.div>

        {/* Image Upload */}
        <motion.div
          className="form-field full-width"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <label className="field-label">
            <FaImage className="label-icon" />
            Event Picture
          </label>

          <div
            className={`image-upload-area ${isDragOver ? 'drag-over' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {imagePreview ? (
              <div className="image-preview-section">
                <img src={imagePreview} alt="Event preview" className="preview-image" />
                <div className="image-overlay">
                  <button
                    type="button"
                    className="change-image-btn"
                    onClick={() => document.getElementById('image-input').click()}
                  >
                    <FaUpload /> Change Image
                  </button>
                </div>
              </div>
            ) : (
              <div className="upload-placeholder">
                <FaUpload className="upload-icon" />
                <h4>Upload Event Image</h4>
                <p>Drag and drop an image here, or click to select</p>
                <button
                  type="button"
                  className="upload-btn"
                  onClick={() => document.getElementById('image-input').click()}
                >
                  Choose File
                </button>
              </div>
            )}

            <input
              id="image-input"
              type="file"
              accept="image/*"
              onChange={handleFileInput}
              style={{ display: 'none' }}
              required={!isEditMode && !imagePreview}
            />
          </div>
        </motion.div>

        {/* Form Actions */}
        <motion.div
          className="form-actions"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          {onCancel && (
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            className="btn btn-primary submit-btn"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="loading-spinner"></div>
                {isEditMode ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <FaSave className="btn-icon" />
                {isEditMode ? 'Update Event' : 'Create Event'}
              </>
            )}
          </button>
        </motion.div>
      </form>
    </motion.div>
  );
};

export default EventForm; 