import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createEventWithImage } from '../../reudx/slice/EventsSlice';
import { selectEventsStatus, selectEventsError } from '../../reudx/slice/EventsSlice';
import { useSelector as useReduxSelector } from 'react-redux';

const EventForm = ({ onSuccess }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectEventsStatus);
  const error = useSelector(selectEventsError);
  const user = useReduxSelector(state => state.auth.user.data);
  
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [votingPrice, setVotingPrice] = useState('');
  const [image, setImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('event[name]', name);
    formData.append('event[description]', description);
    formData.append('event[voting_price]', votingPrice);
    if (image) formData.append('event[image]', image);
    if (user?.id) formData.append('event[user_id]', user.id);
    
    try {
      const result = await dispatch(createEventWithImage(formData)).unwrap();
      onSuccess(result);
      // Reset form
      setName('');
      setDescription('');
      setVotingPrice('');
      setImage(null);
      setImagePreview(null);
    } catch (err) {
      // Error is handled by Redux and available in the error selector
      console.error('Failed to create event:', err);
    }
  };

  return (
    <form className="event-form" onSubmit={handleSubmit} encType="multipart/form-data">
      <h2>Create New Event</h2>
      {error && <div className="error-container">{error}</div>}
      <div className="form-group">
        <label>Event Name</label>
        <input type="text" value={name} onChange={e => setName(e.target.value)} required />
      </div>
      <div className="form-group">
        <label>Description</label>
        <textarea value={description} onChange={e => setDescription(e.target.value)} required />
      </div>
      <div className="form-group">
        <label>Voting Price (GHS)</label>
        <input type="number" min="0" step="0.01" value={votingPrice} onChange={e => setVotingPrice(e.target.value)} required />
      </div>
      <div className="form-group">
        <label>Event Picture</label>
        <input type="file" accept="image/*" onChange={handleImageChange} required />
        {imagePreview && (
          <div className="image-preview-container">
            <h4>Image Preview:</h4>
            <img src={imagePreview} alt="Event preview" className="image-preview" />
          </div>
        )}
      </div>
      <button type="submit" className="btn btn-primary" disabled={status === 'loading'}>
        {status === 'loading' ? 'Creating...' : 'Create Event'}
      </button>
    </form>
  );
};

export default EventForm; 