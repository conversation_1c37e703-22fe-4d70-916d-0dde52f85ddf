import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import {
  FaUser,
  FaImage,
  FaSave,
  FaTimes,
  FaUpload,
  FaCamera,
  FaCheck,
  FaExclamationTriangle
} from 'react-icons/fa';
import { createNomineeWithImage } from '../../reudx/slice/NomineesSlice';
import { selectNomineesStatus, selectNomineesError } from '../../reudx/slice/NomineesSlice';
import { useSelector as useReduxSelector } from 'react-redux';

const NomineeForm = ({ eventId, categoryId, onSuccess, onCancel }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectNomineesStatus);
  const error = useSelector(selectNomineesError);
  const user = useReduxSelector(state => state.auth.user.data);

  const [name, setName] = useState('');
  const [image, setImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleImageChange = (file) => {
    if (file) {
      setImage(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileInput = (e) => {
    const file = e.target.files[0];
    handleImageChange(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleImageChange(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData();
    formData.append('nominee[name]', name);
    formData.append('nominee[vote]', 0);
    if (image) formData.append('nominee[image]', image);
    formData.append('nominee[event_id]', eventId);
    formData.append('nominee[category_id]', categoryId);
    if (user?.id) formData.append('nominee[user_id]', user.id);

    try {
      const result = await dispatch(createNomineeWithImage({ eventId, categoryId, formData })).unwrap();
      onSuccess(result);
      // Reset form
      setName('');
      setImage(null);
      setImagePreview(null);
    } catch (err) {
      console.error('Failed to add nominee:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="modern-nominee-form-container"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Form Header */}
      <div className="nominee-form-header">
        <div className="header-content">
          <div className="header-info">
            <FaUser className="header-icon" />
            <div>
              <h3 className="form-title">Add New Nominee</h3>
              <p className="form-subtitle">Add a nominee to this category</p>
            </div>
          </div>
          {onCancel && (
            <motion.button
              type="button"
              className="close-btn"
              onClick={onCancel}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaTimes />
            </motion.button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <motion.div
          className="error-alert"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
        >
          <FaExclamationTriangle className="error-icon" />
          <span className="error-text">{error}</span>
        </motion.div>
      )}

      {/* Form Content */}
      <form className="modern-nominee-form" onSubmit={handleSubmit} encType="multipart/form-data">
        {/* Nominee Name */}
        <motion.div
          className="form-field"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <label className="field-label">
            <FaUser className="label-icon" />
            Nominee Name
          </label>
          <input
            type="text"
            className="field-input"
            value={name}
            onChange={e => setName(e.target.value)}
            placeholder="Enter nominee's full name..."
            required
          />
        </motion.div>

        {/* Image Upload */}
        <motion.div
          className="form-field"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <label className="field-label">
            <FaImage className="label-icon" />
            Nominee Picture
          </label>

          <div
            className={`nominee-image-upload-area ${isDragOver ? 'drag-over' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {imagePreview ? (
              <div className="nominee-image-preview-section">
                <div className="preview-container">
                  <img src={imagePreview} alt="Nominee preview" className="nominee-preview-image" />
                  <div className="preview-overlay">
                    <button
                      type="button"
                      className="change-image-btn"
                      onClick={() => document.getElementById('nominee-image-input').click()}
                    >
                      <FaCamera /> Change Photo
                    </button>
                  </div>
                </div>
                <div className="preview-info">
                  <FaCheck className="success-icon" />
                  <span>Photo uploaded successfully</span>
                </div>
              </div>
            ) : (
              <div className="upload-placeholder">
                <div className="upload-icon-wrapper">
                  <FaUpload className="upload-icon" />
                </div>
                <h4>Upload Nominee Photo</h4>
                <p>Drag and drop an image here, or click to select</p>
                <button
                  type="button"
                  className="upload-btn"
                  onClick={() => document.getElementById('nominee-image-input').click()}
                >
                  <FaCamera /> Choose Photo
                </button>
              </div>
            )}

            <input
              id="nominee-image-input"
              type="file"
              accept="image/*"
              onChange={handleFileInput}
              style={{ display: 'none' }}
              required={!imagePreview}
            />
          </div>
        </motion.div>

        {/* Form Actions */}
        <motion.div
          className="nominee-form-actions"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {onCancel && (
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            className="btn btn-primary submit-btn"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="loading-spinner"></div>
                Adding...
              </>
            ) : (
              <>
                <FaSave className="btn-icon" />
                Add Nominee
              </>
            )}
          </button>
        </motion.div>
      </form>
    </motion.div>
  );
};

export default NomineeForm; 