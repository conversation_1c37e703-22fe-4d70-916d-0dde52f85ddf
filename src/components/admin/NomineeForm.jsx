import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createNomineeWithImage } from '../../reudx/slice/NomineesSlice';
import { selectNomineesStatus, selectNomineesError } from '../../reudx/slice/NomineesSlice';
import { useSelector as useReduxSelector } from 'react-redux';

const NomineeForm = ({ eventId, categoryId, onSuccess }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectNomineesStatus);
  const error = useSelector(selectNomineesError);
  const user = useReduxSelector(state => state.auth.user.data);
  
  const [name, setName] = useState('');
  const [image, setImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('nominee[name]', name);
    formData.append('nominee[vote]', 0);
    if (image) formData.append('nominee[image]', image);
    formData.append('nominee[event_id]', eventId);
    formData.append('nominee[category_id]', categoryId);
    if (user?.id) formData.append('nominee[user_id]', user.id);
    
    try {
      const result = await dispatch(createNomineeWithImage({ eventId, categoryId, formData })).unwrap();
      onSuccess(result);
      // Reset form
      setName('');
      setImage(null);
      setImagePreview(null);
    } catch (err) {
      // Error is handled by Redux and available in the error selector
      console.error('Failed to add nominee:', err);
    }
  };

  return (
    <form className="nominee-form" onSubmit={handleSubmit} encType="multipart/form-data" style={{ marginTop: 16, marginBottom: 16 }}>
      <h4>Add Nominee</h4>
      {error && <div className="error-container">{error}</div>}
      <div className="form-group">
        <label>Nominee Name</label>
        <input type="text" value={name} onChange={e => setName(e.target.value)} required />
      </div>
      <div className="form-group">
        <label>Nominee Picture</label>
        <input type="file" accept="image/*" onChange={handleImageChange} required />
        {imagePreview && (
          <div className="image-preview-container">
            <h5>Image Preview:</h5>
            <img src={imagePreview} alt="Nominee preview" className="image-preview" />
          </div>
        )}
      </div>
      <button type="submit" className="btn btn-primary" disabled={status === 'loading'}>
        {status === 'loading' ? 'Adding...' : 'Add Nominee'}
      </button>
    </form>
  );
};

export default NomineeForm; 