import React, { useState } from 'react';
import EventForm from './EventForm';
import CategoryForm from './CategoryForm';

const EventWizard = () => {
  const [step, setStep] = useState(1);
  const [eventId, setEventId] = useState(null);
  const [eventData, setEventData] = useState(null);

  // Step 1: Event creation
  const handleEventCreated = (event) => {
    setEventId(event.id);
    setEventData(event);
    setStep(2);
  };

  // Step 2: Category and nominee creation
  const handleBackToEvent = () => {
    setStep(1);
  };

  return (
    <div className="event-wizard-container">
      {step === 1 && (
        <EventForm onSuccess={handleEventCreated} />
      )}
      {step === 2 && eventId && (
        <CategoryForm eventId={eventId} eventData={eventData} onBack={handleBackToEvent} />
      )}
    </div>
  );
};

export default EventWizard; 