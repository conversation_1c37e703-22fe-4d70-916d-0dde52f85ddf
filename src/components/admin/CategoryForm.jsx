import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCategory } from '../../reudx/slice/CategoriesSlice';
import { selectCategoriesStatus, selectCategoriesError } from '../../reudx/slice/CategoriesSlice';
import NomineeForm from './NomineeForm';
import { useSelector as useReduxSelector } from 'react-redux';

const capitalize = (str) => str.replace(/\b\w/g, c => c.toUpperCase());

const CategoryForm = ({ eventId, eventData, onBack }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectCategoriesStatus);
  const error = useSelector(selectCategoriesError);
  const user = useReduxSelector(state => state.auth.user.data);
  
  const [categories, setCategories] = useState([]);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [activeCategoryId, setActiveCategoryId] = useState(null);

  const handleAddCategory = async (e) => {
    e.preventDefault();
    
    const payload = {
      name: capitalize(name.trim()),
      description: description.trim(),
      user_id: user?.id,
    };
    
    try {
      const result = await dispatch(createCategory({ eventId, categoryData: { category: payload } })).unwrap();
      setCategories([...categories, { ...result, nominees: [] }]);
      setName('');
      setDescription('');
    } catch (err) {
      // Error is handled by Redux and available in the error selector
      console.error('Failed to add category:', err);
    }
  };

  const handleNomineeAdded = (categoryId, nominee) => {
    setCategories(categories.map(cat =>
      cat.id === categoryId ? { ...cat, nominees: [...(cat.nominees || []), nominee] } : cat
    ));
    setActiveCategoryId(null);
  };

  return (
    <div className="category-form-container">
      <h2>Add Categories for {eventData?.name}</h2>
      {error && <div className="error-container">{error}</div>}
      <form className="category-form" onSubmit={handleAddCategory}>
        <div className="form-group">
          <label>Category Name</label>
          <input type="text" value={name} onChange={e => setName(e.target.value)} required />
        </div>
        <div className="form-group">
          <label>Description</label>
          <textarea value={description} onChange={e => setDescription(e.target.value)} required />
        </div>
        <button type="submit" className="btn btn-primary" disabled={status === 'loading'}>
          {status === 'loading' ? 'Adding...' : 'Add Category'}
        </button>
        <button type="button" className="btn btn-secondary" onClick={onBack} style={{ marginLeft: 8 }}>
          Back
        </button>
      </form>
      <div className="added-categories-list">
        <h3>Added Categories</h3>
        {categories.length === 0 && <p>No categories added yet.</p>}
        {categories.map((cat) => (
          <div key={cat.id} className="added-category">
            <div className="category-header-row">
              <strong>{cat.name}</strong> <span style={{ color: '#888', marginLeft: 8 }}>{cat.description}</span>
              <button className="btn btn-sm btn-success" style={{ marginLeft: 16 }} onClick={() => setActiveCategoryId(cat.id)}>
                Add Nominee
              </button>
            </div>
            {cat.nominees && cat.nominees.length > 0 && (
              <ul className="nominee-list">
                {cat.nominees.map(nom => (
                  <li key={nom.id}>{nom.name}</li>
                ))}
              </ul>
            )}
            {activeCategoryId === cat.id && (
              <NomineeForm eventId={eventId} categoryId={cat.id} onSuccess={(nominee) => handleNomineeAdded(cat.id, nominee)} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CategoryForm; 