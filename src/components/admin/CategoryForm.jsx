import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaTrophy,
  FaPlus,
  FaArrowLeft,
  FaFileAlt,
  FaSave,
  FaUsers,
  FaEdit,
  FaTrash,
  FaChevronDown,
  FaChevronUp
} from 'react-icons/fa';
import { createCategory } from '../../reudx/slice/CategoriesSlice';
import { selectCategoriesStatus, selectCategoriesError } from '../../reudx/slice/CategoriesSlice';
import NomineeForm from './NomineeForm';
import { useSelector as useReduxSelector } from 'react-redux';

const capitalize = (str) => str.replace(/\b\w/g, c => c.toUpperCase());

const CategoryForm = ({ eventId, eventData, onBack }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectCategoriesStatus);
  const error = useSelector(selectCategoriesError);
  const user = useReduxSelector(state => state.auth.user.data);

  const [categories, setCategories] = useState([]);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [activeCategoryId, setActiveCategoryId] = useState(null);
  const [expandedCategories, setExpandedCategories] = useState(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddCategory = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const payload = {
      name: capitalize(name.trim()),
      description: description.trim(),
      user_id: user?.id,
    };

    try {
      const result = await dispatch(createCategory({ eventId, categoryData: { category: payload } })).unwrap();
      setCategories([...categories, { ...result, nominees: [] }]);
      setName('');
      setDescription('');
      // Auto-expand the newly added category
      setExpandedCategories(prev => new Set([...prev, result.id]));
    } catch (err) {
      console.error('Failed to add category:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleCategoryExpansion = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const handleNomineeAdded = (categoryId, nominee) => {
    setCategories(categories.map(cat =>
      cat.id === categoryId ? { ...cat, nominees: [...(cat.nominees || []), nominee] } : cat
    ));
    setActiveCategoryId(null);
  };

  return (
    <motion.div
      className="modern-category-form-container"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Header */}
      <div className="category-form-header">
        <div className="header-content">
          <motion.button
            className="back-btn"
            onClick={onBack}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaArrowLeft />
          </motion.button>

          <div className="header-info">
            <FaTrophy className="header-icon" />
            <div>
              <h1 className="header-title">Add Categories</h1>
              <p className="header-subtitle">Create categories for "{eventData?.name}"</p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <motion.div
          className="error-alert"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
        >
          <span className="error-text">{error}</span>
        </motion.div>
      )}

      <div className="category-form-content">
        {/* Add Category Form */}
        <motion.div
          className="add-category-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="section-header">
            <h2 className="section-title">
              <FaPlus className="section-icon" />
              Add New Category
            </h2>
          </div>

          <form className="modern-category-form" onSubmit={handleAddCategory}>
            <div className="form-grid">
              <motion.div
                className="form-field"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <label className="field-label">
                  <FaTrophy className="label-icon" />
                  Category Name
                </label>
                <input
                  type="text"
                  className="field-input"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  placeholder="Enter category name..."
                  required
                />
              </motion.div>

              <motion.div
                className="form-field full-width"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <label className="field-label">
                  <FaFileAlt className="label-icon" />
                  Description
                </label>
                <textarea
                  className="field-textarea"
                  value={description}
                  onChange={e => setDescription(e.target.value)}
                  placeholder="Describe this category..."
                  rows={3}
                  required
                />
              </motion.div>
            </div>

            <motion.div
              className="form-actions"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <button
                type="submit"
                className="btn btn-primary submit-btn"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="loading-spinner"></div>
                    Adding...
                  </>
                ) : (
                  <>
                    <FaSave className="btn-icon" />
                    Add Category
                  </>
                )}
              </button>
            </motion.div>
          </form>
        </motion.div>

        {/* Categories List */}
        <motion.div
          className="categories-list-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <div className="section-header">
            <h2 className="section-title">
              <FaTrophy className="section-icon" />
              Added Categories ({categories.length})
            </h2>
          </div>

          {categories.length === 0 ? (
            <div className="empty-state">
              <FaTrophy className="empty-icon" />
              <h3>No categories added yet</h3>
              <p>Start by adding your first category above</p>
            </div>
          ) : (
            <div className="categories-grid">
              <AnimatePresence>
                {categories.map((category, index) => (
                  <motion.div
                    key={category.id}
                    className="category-card"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    layout
                  >
                    <div className="category-card-header">
                      <div className="category-info">
                        <h3 className="category-name">{category.name}</h3>
                        <p className="category-description">{category.description}</p>
                      </div>

                      <div className="category-actions">
                        <motion.button
                          className="expand-btn"
                          onClick={() => toggleCategoryExpansion(category.id)}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          {expandedCategories.has(category.id) ? <FaChevronUp /> : <FaChevronDown />}
                        </motion.button>
                      </div>
                    </div>

                    <AnimatePresence>
                      {expandedCategories.has(category.id) && (
                        <motion.div
                          className="category-card-content"
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <div className="category-stats">
                            <div className="stat-item">
                              <FaUsers className="stat-icon" />
                              <span>{category.nominees?.length || 0} nominees</span>
                            </div>
                          </div>

                          {category.nominees && category.nominees.length > 0 && (
                            <div className="nominees-list">
                              <h4>Nominees:</h4>
                              <div className="nominees-grid">
                                {category.nominees.map(nominee => (
                                  <div key={nominee.id} className="nominee-tag">
                                    {nominee.name}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          <div className="category-card-actions">
                            <button
                              className="btn btn-primary btn-sm"
                              onClick={() => setActiveCategoryId(category.id)}
                            >
                              <FaPlus /> Add Nominee
                            </button>
                          </div>

                          {activeCategoryId === category.id && (
                            <motion.div
                              className="nominee-form-section"
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                            >
                              <NomineeForm
                                eventId={eventId}
                                categoryId={category.id}
                                onSuccess={(nominee) => handleNomineeAdded(category.id, nominee)}
                              />
                            </motion.div>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default CategoryForm; 