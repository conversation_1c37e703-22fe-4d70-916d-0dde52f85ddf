import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAdminStatistics, selectAdminStatistics, selectAdminStatisticsStatus, selectAdminStatisticsError, deleteEvent } from '../../reudx/slice/EventsSlice';
import { selectUserRole } from '../../reudx/slice/AuthSlice';
import { Link, useNavigate } from 'react-router-dom';
import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import EventForm from './EventForm';
import CategoryForm from './CategoryForm';
import NomineeForm from './NomineeForm';
import { deleteCategory } from '../../reudx/slice/CategoriesSlice';
import { deleteNominee } from '../../reudx/slice/NomineesSlice';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const AdminDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const role = useSelector(selectUserRole);
  const stats = useSelector(selectAdminStatistics);
  const status = useSelector(selectAdminStatisticsStatus);
  const error = useSelector(selectAdminStatisticsError);

  // Modal state
  const [modal, setModal] = useState({ type: null, data: null });

  useEffect(() => {
    if (role !== 'admin') {
      navigate('/');
    } else {
      dispatch(fetchAdminStatistics());
    }
  }, [dispatch, role, navigate]);

  // Chart data
  const eventNames = stats?.events.map(e => e.event_name) || [];
  const eventVotes = stats?.events.map(e => e.total_paid_votes) || [];
  const eventRevenue = stats?.events.map(e => e.total_revenue) || [];

  const barData = {
    labels: eventNames,
    datasets: [
      {
        label: 'Paid Votes',
        data: eventVotes,
        backgroundColor: 'rgba(99, 102, 241, 0.7)',
      },
      {
        label: 'Revenue (GHS)',
        data: eventRevenue,
        backgroundColor: 'rgba(16, 185, 129, 0.7)',
      },
    ],
  };

  const doughnutData = {
    labels: eventNames,
    datasets: [
      {
        label: 'Votes',
        data: eventVotes,
        backgroundColor: [
          '#818cf8', '#f59e0b', '#10b981', '#ef4444', '#f472b6', '#38bdf8', '#fbbf24', '#a3e635', '#f87171', '#c084fc'
        ],
      },
    ],
  };

  // Edit/Delete handlers
  const handleEdit = (type, data) => setModal({ type: `edit-${type}`, data });
  const handleDelete = (type, data) => setModal({ type: `delete-${type}`, data });
  const closeModal = () => setModal({ type: null, data: null });

  // Confirm delete for event
  const confirmDeleteEvent = async (eventId) => {
    try {
      await dispatch(deleteEvent(eventId)).unwrap();
      closeModal();
      dispatch(fetchAdminStatistics());
    } catch (err) {
      alert('Failed to delete event.');
    }
  };

  // Placeholder for category/nominee delete (implement with your slice thunks)
  const confirmDeleteCategory = async (eventId, categoryId) => {
    try {
      await dispatch(deleteCategory({ eventId, categoryId })).unwrap();
      closeModal();
      dispatch(fetchAdminStatistics());
    } catch (err) {
      alert('Failed to delete category.');
    }
  };
  const confirmDeleteNominee = async (eventId, nomineeId) => {
    alert('Delete nominee requires categoryId. Please extend the dashboard data to include categoryId for nominees.');
    closeModal();
  };

  if (role !== 'admin') return null;

  return (
    <div className="admin-dashboard-container">
      <h1>Admin Dashboard</h1>
      {status === 'loading' && <p>Loading statistics...</p>}
      {error && <div className="error-container">{error}</div>}
      {stats && (
        <>
          {/* Charts */}
          <div className="admin-charts-row">
            <div className="admin-chart-card">
              <h3>Paid Votes & Revenue by Event</h3>
              <Bar data={barData} options={{ responsive: true, plugins: { legend: { position: 'top' } } }} />
            </div>
            <div className="admin-chart-card">
              <h3>Votes Distribution</h3>
              <Doughnut data={doughnutData} options={{ responsive: true, plugins: { legend: { position: 'bottom' } } }} />
            </div>
          </div>
          <div className="admin-stats-summary">
            <div className="stat-card">
              <h2>Total Users</h2>
              <p>{stats.total_users}</p>
            </div>
            <div className="stat-card">
              <h2>Total Events</h2>
              <p>{stats.events.length}</p>
            </div>
          </div>
          <div className="admin-events-list">
            <h2>Events</h2>
            {stats.events.map(event => (
              <div key={event.event_id} className="admin-event-card">
                <h3>{event.event_name}</h3>
                <p>Total Paid Votes: {event.total_paid_votes}</p>
                <p>Total Revenue: GHS {event.total_revenue}</p>
                <div className="admin-event-actions">
                  <Link to={`/events/${event.event_id}`}>View</Link>
                  <button onClick={() => handleEdit('event', event)}>Edit</button>
                  <button onClick={() => handleDelete('event', event)}>Delete</button>
                </div>
                <div className="admin-categories-list">
                  <h4>Categories</h4>
                  {event.categories.map(cat => (
                    <div key={cat.category_id} className="admin-category-card">
                      <span>{cat.category_name}</span>
                      <span>Total Votes: {cat.total_votes}</span>
                      <button onClick={() => handleEdit('category', { ...cat, event_id: event.event_id })}>Edit</button>
                      <button onClick={() => handleDelete('category', { ...cat, event_id: event.event_id })}>Delete</button>
                    </div>
                  ))}
                </div>
                <div className="admin-top-nominees-list">
                  <h4>Top Nominees</h4>
                  {event.top_nominees.map(nom => (
                    <div key={nom.nominee_id} className="admin-nominee-card">
                      <span>{nom.name}</span>
                      <span>Votes: {nom.votes}</span>
                      <button onClick={() => handleEdit('nominee', { ...nom, event_id: event.event_id })}>Edit</button>
                      <button onClick={() => handleDelete('nominee', { ...nom, event_id: event.event_id })}>Delete</button>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <div className="admin-create-actions">
            <Link to="/admin/create-event" className="btn btn-primary">Create New Event</Link>
          </div>

          {/* Modals for edit/delete */}
          {modal.type === 'edit-event' && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h2>Edit Event</h2>
                <EventForm eventData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchAdminStatistics()); }} />
                <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
              </div>
            </div>
          )}
          {modal.type === 'delete-event' && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h2>Delete Event</h2>
                <p>Are you sure you want to delete <b>{modal.data.event_name}</b>?</p>
                <button onClick={() => confirmDeleteEvent(modal.data.event_id)} className="btn btn-danger">Delete</button>
                <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
              </div>
            </div>
          )}
          {modal.type === 'edit-category' && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h2>Edit Category</h2>
                <CategoryForm eventId={modal.data.event_id} categoryData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchAdminStatistics()); }} />
                <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
              </div>
            </div>
          )}
          {modal.type === 'delete-category' && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h2>Delete Category</h2>
                <p>Are you sure you want to delete <b>{modal.data.category_name}</b>?</p>
                <button onClick={() => confirmDeleteCategory(modal.data.event_id, modal.data.category_id)} className="btn btn-danger">Delete</button>
                <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
              </div>
            </div>
          )}
          {modal.type === 'edit-nominee' && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h2>Edit Nominee</h2>
                <NomineeForm eventId={modal.data.event_id} nomineeData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchAdminStatistics()); }} />
                <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
              </div>
            </div>
          )}
          {modal.type === 'delete-nominee' && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h2>Delete Nominee</h2>
                <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
                <button onClick={() => confirmDeleteNominee(modal.data.event_id, modal.data.nominee_id)} className="btn btn-danger">Delete</button>
                <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AdminDashboard; 