import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaUsers,
  FaChartBar,
  FaDollarSign,
  FaCalendarAlt,
  FaVoteYea,
  FaEye,
  FaShieldAlt,
  FaCog,
  FaEdit,
  FaTrash
} from 'react-icons/fa';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
} from 'chart.js';
import { TrophyIcon, ChartIcon } from '../icons/CustomIcons';
import LoadingSpinner from '../LoadingSpinner';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { user } = useSelector(state => state.auth);
  const [activeTab, setActiveTab] = useState('overview');
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is admin
  const isAdmin = user?.data?.role === 'admin';

  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
      return;
    }
    fetchStatistics();
  }, [isAdmin, navigate]);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('https://server1.easevote.org/admin/statistics', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setStatistics(data);
    } catch (err) {
      setError(err.message);
      console.error('Failed to fetch admin statistics:', err);
    } finally {
      setLoading(false);
    }
  };

  // Redirect non-admin users
  if (!isAdmin) {
    return (
      <div className="admin-access-denied">
        <motion.div
          className="access-denied-card"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <FaShieldAlt className="access-denied-icon" />
          <h2>Access Denied</h2>
          <p>You don't have permission to access the admin dashboard.</p>
          <p>Only administrators can view this page.</p>
        </motion.div>
      </div>
    );
  }

  if (loading) {
    return <LoadingSpinner message="Loading admin dashboard..." type="pulse" />;
  }

  if (error) {
    return (
      <div className="admin-error">
        <h2>Error Loading Dashboard</h2>
        <p>{error}</p>
        <button onClick={fetchStatistics} className="btn btn-primary">
          Retry
        </button>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaChartBar },
    { id: 'events', label: 'Events', icon: FaCalendarAlt },
    { id: 'users', label: 'Users', icon: FaUsers },
    { id: 'settings', label: 'Settings', icon: FaCog }
  ];

  const totalRevenue = statistics?.events?.reduce((sum, event) => sum + event.total_revenue, 0) || 0;
  const totalVotes = statistics?.events?.reduce((sum, event) => sum + event.total_paid_votes, 0) || 0;
  const totalEvents = statistics?.events?.length || 0;

  // Chart data
  const eventNames = statistics?.events?.map(e => e.event_name) || [];
  const eventVotes = statistics?.events?.map(e => e.total_paid_votes) || [];
  const eventRevenue = statistics?.events?.map(e => e.total_revenue) || [];

  const barData = {
    labels: eventNames,
    datasets: [
      {
        label: 'Paid Votes',
        data: eventVotes,
        backgroundColor: 'rgba(99, 102, 241, 0.8)',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 2,
        borderRadius: 8,
      },
      {
        label: 'Revenue (GH₵)',
        data: eventRevenue,
        backgroundColor: 'rgba(16, 185, 129, 0.8)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 2,
        borderRadius: 8,
      },
    ],
  };

  const doughnutData = {
    labels: eventNames,
    datasets: [
      {
        label: 'Votes Distribution',
        data: eventVotes,
        backgroundColor: [
          'rgba(99, 102, 241, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)',
          'rgba(14, 165, 233, 0.8)',
          'rgba(34, 197, 94, 0.8)',
        ],
        borderColor: [
          'rgba(99, 102, 241, 1)',
          'rgba(16, 185, 129, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(239, 68, 68, 1)',
          'rgba(139, 92, 246, 1)',
          'rgba(236, 72, 153, 1)',
          'rgba(14, 165, 233, 1)',
          'rgba(34, 197, 94, 1)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#f8fafc',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(30, 41, 59, 0.9)',
        titleColor: '#f8fafc',
        bodyColor: '#cbd5e1',
        borderColor: 'rgba(99, 102, 241, 0.5)',
        borderWidth: 1,
      }
    },
    scales: {
      x: {
        ticks: {
          color: '#cbd5e1',
          font: {
            family: 'Inter, sans-serif'
          }
        },
        grid: {
          color: 'rgba(203, 213, 225, 0.1)'
        }
      },
      y: {
        ticks: {
          color: '#cbd5e1',
          font: {
            family: 'Inter, sans-serif'
          }
        },
        grid: {
          color: 'rgba(203, 213, 225, 0.1)'
        }
      }
    }
  };



  return (
    <div className="admin-dashboard">
      {/* Header */}
      <motion.header
        className="admin-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="admin-header-content">
          <div className="admin-title">
            <FaShieldAlt className="admin-icon" />
            <h1>Admin Dashboard</h1>
          </div>
          <div className="admin-user-info">
            <span>Welcome, {user?.data?.name}</span>
            <div className="admin-badge">Administrator</div>
          </div>
        </div>
      </motion.header>

      {/* Navigation Tabs */}
      <motion.nav
        className="admin-nav"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="admin-nav-tabs">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`admin-nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              >
                <IconComponent className="tab-icon" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </motion.nav>

      {/* Main Content */}
      <main className="admin-main">
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="admin-overview"
            >
              {/* Quick Stats */}
              <div className="admin-stats-grid">
                <motion.div
                  className="admin-stat-card users"
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="stat-icon">
                    <FaUsers />
                  </div>
                  <div className="stat-content">
                    <h3>Total Users</h3>
                    <p className="stat-number">{statistics?.total_users || 0}</p>
                  </div>
                </motion.div>

                <motion.div
                  className="admin-stat-card revenue"
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="stat-icon">
                    <FaDollarSign />
                  </div>
                  <div className="stat-content">
                    <h3>Total Revenue</h3>
                    <p className="stat-number">GH₵{totalRevenue.toFixed(2)}</p>
                  </div>
                </motion.div>

                <motion.div
                  className="admin-stat-card votes"
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="stat-icon">
                    <FaVoteYea />
                  </div>
                  <div className="stat-content">
                    <h3>Total Votes</h3>
                    <p className="stat-number">{totalVotes}</p>
                  </div>
                </motion.div>

                <motion.div
                  className="admin-stat-card events"
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="stat-icon">
                    <FaCalendarAlt />
                  </div>
                  <div className="stat-content">
                    <h3>Active Events</h3>
                    <p className="stat-number">{totalEvents}</p>
                  </div>
                </motion.div>
              </div>

              {/* Charts Section */}
              <div className="admin-charts-section">
                <div className="charts-grid">
                  <motion.div
                    className="chart-card"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <h3>Votes & Revenue by Event</h3>
                    <div className="chart-container">
                      <Bar data={barData} options={chartOptions} />
                    </div>
                  </motion.div>

                  <motion.div
                    className="chart-card"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <h3>Votes Distribution</h3>
                    <div className="chart-container">
                      <Doughnut data={doughnutData} options={{
                        ...chartOptions,
                        plugins: {
                          ...chartOptions.plugins,
                          legend: {
                            ...chartOptions.plugins.legend,
                            position: 'bottom'
                          }
                        }
                      }} />
                    </div>
                  </motion.div>
                </div>
              </div>
              {/* Recent Events Overview */}
              <div className="admin-recent-events">
                <h2 className="section-title">Recent Events</h2>
                <div className="events-overview-grid">
                  {statistics?.events?.slice(0, 3).map((event) => (
                    <motion.div
                      key={event.event_id}
                      className="event-overview-card"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      whileHover={{ scale: 1.02, y: -5 }}
                    >
                      <div className="event-header">
                        <TrophyIcon size={24} color="#ffd700" />
                        <h3>{event.event_name}</h3>
                      </div>
                      <div className="event-stats">
                        <div className="event-stat">
                          <span className="stat-label">Votes:</span>
                          <span className="stat-value">{event.total_paid_votes}</span>
                        </div>
                        <div className="event-stat">
                          <span className="stat-label">Revenue:</span>
                          <span className="stat-value">GH₵{event.total_revenue}</span>
                        </div>
                        <div className="event-stat">
                          <span className="stat-label">Categories:</span>
                          <span className="stat-value">{event.categories?.length || 0}</span>
                        </div>
                      </div>
                      <div className="event-actions">
                        <button className="btn btn-sm btn-primary">
                          <FaEye /> View Details
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'events' && (
            <motion.div
              key="events"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="admin-events"
            >
              <div className="events-header">
                <h2 className="section-title">Event Management</h2>
                <button className="btn btn-primary">
                  <FaCalendarAlt /> Create New Event
                </button>
              </div>

              <div className="events-list">
                {statistics?.events?.map((event) => (
                  <motion.div
                    key={event.event_id}
                    className="admin-event-card"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="event-card-header">
                      <div className="event-info">
                        <h3>{event.event_name}</h3>
                        <div className="event-meta">
                          <span className="event-votes">{event.total_paid_votes} votes</span>
                          <span className="event-revenue">GH₵{event.total_revenue}</span>
                        </div>
                      </div>
                      <div className="event-actions">
                        <button className="btn btn-sm btn-secondary">
                          <FaEdit /> Edit
                        </button>
                        <button className="btn btn-sm btn-danger">
                          <FaTrash /> Delete
                        </button>
                      </div>
                    </div>

                    <div className="event-details">
                      <div className="categories-section">
                        <h4>Categories ({event.categories?.length || 0})</h4>
                        <div className="categories-grid">
                          {event.categories?.map((category) => (
                            <div key={category.category_id} className="category-chip">
                              <span>{category.category_name}</span>
                              <span className="category-votes">{category.total_votes} votes</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="top-nominees-section">
                        <h4>Top Nominees</h4>
                        <div className="nominees-list">
                          {event.top_nominees?.map((nominee, index) => (
                            <div key={nominee.nominee_id} className="nominee-item">
                              <div className="nominee-rank">#{index + 1}</div>
                              <div className="nominee-info">
                                <span className="nominee-name">{nominee.name}</span>
                                <span className="nominee-votes">{nominee.votes} votes</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === 'users' && (
            <motion.div
              key="users"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="admin-users"
            >
              <h2 className="section-title">User Management</h2>
              <div className="users-stats">
                <div className="user-stat-card">
                  <FaUsers className="stat-icon" />
                  <div>
                    <h3>Total Users</h3>
                    <p>{statistics?.total_users || 0}</p>
                  </div>
                </div>
              </div>
              <p className="coming-soon">Detailed user management features coming soon...</p>
            </motion.div>
          )}

          {activeTab === 'settings' && (
            <motion.div
              key="settings"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="admin-settings"
            >
              <h2 className="section-title">System Settings</h2>
              <p className="coming-soon">System settings and configuration options coming soon...</p>
            </motion.div>
          )}
        </AnimatePresence>
      </main>
    </div>
  );

};

export default AdminDashboard; 