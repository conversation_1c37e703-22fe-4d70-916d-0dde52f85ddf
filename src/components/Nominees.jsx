import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import {
  fetchNomineesByCategory, selectNominees, selectNomineesStatus,
  selectNomineesError, updateVote,
} from '../reudx/slice/NomineesSlice';
import VoteForm from './VoteForm';

const Nominees = ({ category }) => {
  const dispatch = useDispatch();
  const nominees = useSelector(selectNominees);
  const status = useSelector(selectNomineesStatus);
  const error = useSelector(selectNomineesError);

  useEffect(() => {
    dispatch(fetchNomineesByCategory(category));
  }, [dispatch, category]);

  if (status === 'loading') {
    return <div className="loading">Loading...</div>;
  }

  if (status === 'failed') {
    return (
      <div className="loading">
        Error: No candidate has been identified in this category.
        {' '}
        {error}
      </div>
    );
  }

  const handleVote = (nomineeId, vote) => {
    dispatch(
      updateVote({
        id: nomineeId,
        vote,
      }),
    );
  };

  return (
    <div className="card-container">
      {nominees.map((nominee) => (
        <div className="card-container-inner" key={nominee.id}>
          <img src={nominee.image_url} alt={nominee.name} />
          <h2>
            Name:
            {' '}
            {nominee.name}
          </h2>
          <h2>
            Code:
            {' '}
            {nominee.nominee_code}
          </h2>
          <VoteForm nomineeId={nominee.id} handleVote={handleVote} />
        </div>
      ))}
    </div>
  );
};

Nominees.propTypes = {
  category: PropTypes.string.isRequired,
};

export default Nominees;
