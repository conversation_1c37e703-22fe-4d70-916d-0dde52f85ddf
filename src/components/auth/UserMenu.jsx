import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { signOutUser, selectUser } from '../../reudx/slice/AuthSlice';
import { motion, AnimatePresence } from 'framer-motion';
import { FaShieldAlt, FaUser, FaFileAlt } from 'react-icons/fa';

const UserMenu = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dispatch = useDispatch();
  const user = useSelector(selectUser);

  // Check if user is admin
  const isAdmin = user?.data?.role === 'admin';

  const handleLogout = () => {
    dispatch(signOutUser());
    setIsOpen(false);
  };

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  return (
    <div className="user-menu">
      <motion.button
        className="user-menu-button"
        onClick={toggleMenu}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <span className="user-avatar">👤</span>
        <span className="user-name">{user?.username || 'User'}</span>
        <span className="menu-arrow">{isOpen ? '▲' : '▼'}</span>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="user-dropdown"
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <div className="user-info">
              <div className="user-details">
                <span className="user-avatar-large">👤</span>
                <div>
                  <p className="username">{user?.data?.name || user?.username}</p>
                  <p className="user-role">{isAdmin ? 'Administrator' : 'Voter'}</p>
                </div>
              </div>
            </div>

            <div className="menu-divider"></div>

            {/* Menu Items */}
            <div className="menu-items">
              {isAdmin && (
                <Link
                  to="/admin/dashboard"
                  className="menu-item admin-link"
                  onClick={closeMenu}
                >
                  <FaShieldAlt className="menu-icon" />
                  <span>Admin Dashboard</span>
                </Link>
              )}

              <Link
                to="/my-submissions"
                className="menu-item"
                onClick={closeMenu}
              >
                <FaFileAlt className="menu-icon" />
                <span>My Submissions</span>
              </Link>

              <Link
                to="/profile"
                className="menu-item"
                onClick={closeMenu}
              >
                <FaUser className="menu-icon" />
                <span>Profile</span>
              </Link>
            </div>

            <div className="menu-divider"></div>

            <motion.button
              className="menu-item logout-btn"
              onClick={handleLogout}
              whileHover={{ backgroundColor: '#ff4757' }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="menu-icon">🚪</span>
              <span>Sign Out</span>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UserMenu; 