import React from 'react';
import { motion } from 'framer-motion';
import { FaTrophy, FaMedal, FaAward } from 'react-icons/fa';

const LoadingSpinner = ({ message = "Loading...", type = "default" }) => {
  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const floatVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const renderSpinner = () => {
    switch (type) {
      case 'awards':
        return (
          <div className="loading-awards">
            <motion.div
              className="award-icon trophy"
              variants={floatVariants}
              animate="animate"
            >
              <FaTrophy />
            </motion.div>
            <motion.div
              className="award-icon medal"
              variants={floatVariants}
              animate="animate"
              style={{ animationDelay: '0.5s' }}
            >
              <FaMedal />
            </motion.div>
            <motion.div
              className="award-icon award"
              variants={floatVariants}
              animate="animate"
              style={{ animationDelay: '1s' }}
            >
              <FaAward />
            </motion.div>
          </div>
        );
      
      case 'pulse':
        return (
          <motion.div
            className="loading-pulse"
            variants={pulseVariants}
            animate="animate"
          >
            <div className="pulse-circle"></div>
          </motion.div>
        );
      
      case 'dots':
        return (
          <div className="loading-dots">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className="dot"
                animate={{
                  y: [-10, 10, -10],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        );
      
      default:
        return (
          <motion.div
            className="loading-spinner-default"
            variants={spinnerVariants}
            animate="animate"
          >
            <div className="spinner-ring"></div>
          </motion.div>
        );
    }
  };

  return (
    <motion.div
      className="loading-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {renderSpinner()}
      <motion.p
        className="loading-message"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {message}
      </motion.p>
    </motion.div>
  );
};

export default LoadingSpinner;
