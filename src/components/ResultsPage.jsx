import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  CartesianGrid,
} from 'recharts';
import { fetchNominees } from '../reudx/slice/NomineesSlice';

const ResultsPage = () => {
  const dispatch = useDispatch();
  const nominees = useSelector((state) => state.nominees.nominees);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [filteredNominees, setFilteredNominees] = useState([]);

  useEffect(() => {
    dispatch(fetchNominees());
  }, [dispatch]);

  // Extract unique categories and sort them alphabetically
  const categories = [...new Set(nominees.map((nominee) => nominee.category))].sort();

  useEffect(() => {
    // Filter nominees based on selected category
    const filtered = selectedCategory
      ? nominees.filter((nominee) => nominee.category === selectedCategory)
      : nominees; // Show all nominees if no category is selected
    setFilteredNominees(filtered);
  }, [selectedCategory, nominees]);

  // Group nominees by category
  const groupedData = filteredNominees.reduce((acc, nominee) => {
    const { category, name, vote } = nominee;
    if (!acc[category]) {
      acc[category] = { category, votes: [] };
    }
    acc[category].votes.push({ name, vote });
    return acc;
  }, {});

  // Prepare data for Recharts
  const chartData = Object.values(groupedData).flatMap((group) => group.votes.map((vote) => ({
    category: group.category,
    name: vote.name,
    vote: vote.vote,
  })));

  return (
    <div className="results-page">
      <h1>Vote Results</h1>

      {/* Dropdown to select category */}
      <select
        value={selectedCategory}
        onChange={(e) => setSelectedCategory(e.target.value)}
        className="category-dropdown"
      >
        <option value="">Select a category...</option>
        {categories.map((category) => (
          <option key={category} value={category}>
            {category}
          </option>
        ))}
      </select>

      {chartData.length > 0 ? (
        <div className="chart-container">
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={chartData}
              margin={{
                top: 0, right: 0, left: 0, bottom: 150,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
              />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="vote" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <p className="no-res">No results found for the selected category.</p>
      )}
    </div>
  );
};

export default ResultsPage;
