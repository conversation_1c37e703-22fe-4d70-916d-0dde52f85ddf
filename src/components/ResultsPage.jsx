import React, { useEffect, useState, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import { FaChartBar, FaChartPie, FaChartLine, FaTrophy, FaMedal, FaAward } from 'react-icons/fa';
import { fetchEvents } from '../reudx/slice/EventsSlice';
import { fetchNomineesByEvent } from '../reudx/slice/NomineesSlice';
import { selectEvents, selectEventsStatus as selectEventsLoading } from '../reudx/slice/EventsSlice';
import { selectNominees, selectNomineesStatus, selectNomineesError } from '../reudx/slice/NomineesSlice';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

const ResultsPage = () => {
  const dispatch = useDispatch();
  const events = useSelector(selectEvents);
  const eventsLoading = useSelector(selectEventsLoading);
  const nominees = useSelector(selectNominees);
  const nomineesStatus = useSelector(selectNomineesStatus);
  const nomineesError = useSelector(selectNomineesError);

  const [selectedEvent, setSelectedEvent] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [chartType, setChartType] = useState('bar');
  const [animationKey, setAnimationKey] = useState(0);

  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  useEffect(() => {
    if (selectedEvent) {
      dispatch(fetchNomineesByEvent(selectedEvent));
    }
  }, [selectedEvent, dispatch]);

  // Extract unique categories and sort them alphabetically
  const categories = useMemo(() => (
    [...new Set(nominees.map((nominee) => nominee.category))].sort()
  ), [nominees]);

  // Group nominees by category and sort by votes
  const groupedData = useMemo(() => {
    const acc = {};
    nominees.forEach((nominee) => {
      const category = nominee.category;
      if (!acc[category]) acc[category] = [];
      acc[category].push(nominee);
    });
    Object.keys(acc).forEach((category) => {
      acc[category].sort((a, b) => b.vote - a.vote);
    });
    return acc;
  }, [nominees]);

  // Prepare data for different chart types
  const prepareChartData = () => {
    const filteredCategories = selectedCategory
      ? [selectedCategory]
      : categories;
    if (chartType === 'bar') {
      return {
        labels: filteredCategories,
        datasets: [
          {
            label: 'Total Votes',
            data: filteredCategories.map(category =>
              groupedData[category]?.reduce((sum, nominee) => sum + nominee.vote, 0) || 0
            ),
            backgroundColor: 'rgba(99, 102, 241, 0.8)',
            borderColor: 'rgba(99, 102, 241, 1)',
            borderWidth: 2,
            borderRadius: 8,
          },
          {
            label: 'Winner Votes',
            data: filteredCategories.map(category =>
              groupedData[category]?.[0]?.vote || 0
            ),
            backgroundColor: 'rgba(16, 185, 129, 0.8)',
            borderColor: 'rgba(16, 185, 129, 1)',
            borderWidth: 2,
            borderRadius: 8,
          }
        ]
      };
    } else if (chartType === 'doughnut') {
      const totalVotesByCategory = filteredCategories.map(category =>
        groupedData[category]?.reduce((sum, nominee) => sum + nominee.vote, 0) || 0
      );
      return {
        labels: filteredCategories,
        datasets: [{
          data: totalVotesByCategory,
          backgroundColor: [
            'rgba(99, 102, 241, 0.8)',
            'rgba(16, 185, 129, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(139, 92, 246, 0.8)',
            'rgba(236, 72, 153, 0.8)',
            'rgba(14, 165, 233, 0.8)',
            'rgba(34, 197, 94, 0.8)',
          ],
          borderColor: [
            'rgba(99, 102, 241, 1)',
            'rgba(16, 185, 129, 1)',
            'rgba(245, 158, 11, 1)',
            'rgba(239, 68, 68, 1)',
            'rgba(139, 92, 246, 1)',
            'rgba(236, 72, 153, 1)',
            'rgba(14, 165, 233, 1)',
            'rgba(34, 197, 94, 1)',
          ],
          borderWidth: 2,
        }]
      };
    } else if (chartType === 'line') {
      return {
        labels: filteredCategories,
        datasets: [{
          label: 'Vote Trends',
          data: filteredCategories.map(category =>
            groupedData[category]?.reduce((sum, nominee) => sum + nominee.vote, 0) || 0
          ),
          borderColor: 'rgba(99, 102, 241, 1)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: 'rgba(99, 102, 241, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 6,
        }]
      };
    }
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#f8fafc',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          }
        }
      },
      title: {
        display: true,
        text: selectedCategory ? `Results for ${selectedCategory}` : 'Voting Results Overview',
        color: '#f8fafc',
        font: {
          family: 'Poppins, sans-serif',
          size: 16,
          weight: 'bold'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(30, 41, 59, 0.9)',
        titleColor: '#f8fafc',
        bodyColor: '#cbd5e1',
        borderColor: 'rgba(99, 102, 241, 0.5)',
        borderWidth: 1,
      }
    },
    scales: chartType !== 'doughnut' ? {
      x: {
        ticks: {
          color: '#cbd5e1',
          font: {
            family: 'Inter, sans-serif'
          }
        },
        grid: {
          color: 'rgba(203, 213, 225, 0.1)'
        }
      },
      y: {
        ticks: {
          color: '#cbd5e1',
          font: {
            family: 'Inter, sans-serif'
          }
        },
        grid: {
          color: 'rgba(203, 213, 225, 0.1)'
        }
      }
    } : {}
  };

  const handleChartTypeChange = (newType) => {
    setChartType(newType);
    setAnimationKey(prev => prev + 1);
  };

  // Loading state
  if (eventsLoading === 'loading') {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading events...</p>
      </div>
    );
  }

  // Error state
  if (nomineesError) {
    return (
      <div className="error-container">
        <h2>Error Loading Results</h2>
        <p>{nomineesError}</p>
        <button onClick={() => selectedEvent && dispatch(fetchNomineesByEvent(selectedEvent))} className="btn btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <Link to="/" className="logo">🏆 EaseVote</Link>
          <nav>
            <Link to="/" className="btn btn-secondary btn-sm">
              ← Back to Categories
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <div className="main-container">
        <motion.div
          className="results-page"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="results-header">
            <h1 className="results-title">🏆 Voting Results</h1>
            <p className="results-subtitle">Discover the winners and vote statistics</p>
          </div>

          {/* Event Selection */}
          <div className="results-controls">
            <div className="control-group">
              <label className="control-label">Select Event:</label>
              <select
                value={selectedEvent}
                onChange={e => setSelectedEvent(e.target.value)}
                className="form-select"
              >
                <option value="">Choose an event...</option>
                {events.map((event) => (
                  <option key={event.id} value={event.id}>
                    {event.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Show content only if event is selected */}
          {selectedEvent && (
            <>
              {/* Controls */}
              <div className="results-controls">
                <div className="control-group">
                  <label className="control-label">Category Filter:</label>
                  <select
                    value={selectedCategory}
                    onChange={e => setSelectedCategory(e.target.value)}
                    className="form-select"
                  >
                    <option value="">All Categories</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="control-group">
                  <label className="control-label">Chart Type:</label>
                  <div className="chart-type-buttons">
                    <button
                      onClick={() => handleChartTypeChange('bar')}
                      className={`btn btn-sm ${chartType === 'bar' ? 'btn-primary' : 'btn-secondary'}`}
                    >
                      <FaChartBar /> Bar
                    </button>
                    <button
                      onClick={() => handleChartTypeChange('doughnut')}
                      className={`btn btn-sm ${chartType === 'doughnut' ? 'btn-primary' : 'btn-secondary'}`}
                    >
                      <FaChartPie /> Pie
                    </button>
                    <button
                      onClick={() => handleChartTypeChange('line')}
                      className={`btn btn-sm ${chartType === 'line' ? 'btn-primary' : 'btn-secondary'}`}
                    >
                      <FaChartLine /> Line
                    </button>
                  </div>
                </div>
              </div>

              {/* Loading state for nominees */}
              {nomineesStatus === 'loading' ? (
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>Loading nominees...</p>
                </div>
              ) : nominees.length === 0 ? (
                <div className="no-results">
                  <h2>No nominees found for this event</h2>
                  <p>There are no nominees available for the selected event.</p>
                </div>
              ) : (
                <>
                  {/* Chart */}
                  <motion.div
                    className="chart-section"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <div className="chart-container">
                      {(() => {
                        const data = prepareChartData();
                        switch (chartType) {
                          case 'bar':
                            return <Bar key={animationKey} data={data} options={chartOptions} />;
                          case 'doughnut':
                            return <Doughnut key={animationKey} data={data} options={chartOptions} />;
                          case 'line':
                            return <Line key={animationKey} data={data} options={chartOptions} />;
                          default:
                            return <Bar key={animationKey} data={data} options={chartOptions} />;
                        }
                      })()}
                    </div>
                  </motion.div>

                  {/* Winners Section */}
                  <motion.div
                    className="winners-section"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                  >
                    <h2 className="section-title">🏆 Category Winners</h2>
                    <div className="winners-grid">
                      {Object.keys(groupedData).map((category) => {
                        const winner = groupedData[category][0];
                        const totalVotes = groupedData[category].reduce((sum, nominee) => sum + nominee.vote, 0);
                        return (
                          <motion.div
                            key={category}
                            className="winner-card"
                            whileHover={{ scale: 1.02, y: -5 }}
                            transition={{ duration: 0.2 }}
                          >
                            <div className="winner-header">
                              <FaTrophy className="trophy-icon" />
                              <h3 className="category-name">{category}</h3>
                            </div>
                            {winner && (
                              <div className="winner-info">
                                <img
                                  src={winner.image_url}
                                  alt={winner.name}
                                  className="winner-image"
                                />
                                <div className="winner-details">
                                  <h4 className="winner-name">{winner.name}</h4>
                                  <div className="vote-stats">
                                    <span className="winner-votes">
                                      <FaMedal /> {winner.vote} votes
                                    </span>
                                    <span className="total-votes">
                                      Total: {totalVotes} votes
                                    </span>
                                  </div>
                                  <div className="vote-percentage">
                                    {((winner.vote / totalVotes) * 100).toFixed(1)}% of votes
                                  </div>
                                </div>
                              </div>
                            )}
                          </motion.div>
                        );
                      })}
                    </div>
                  </motion.div>

                  {/* Detailed Results */}
                  <motion.div
                    className="detailed-results"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    <h2 className="section-title">�� Detailed Results</h2>
                    <AnimatePresence>
                      {Object.keys(groupedData).map((category) => (
                        <motion.div
                          key={category}
                          className="category-results"
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <h3 className="category-title">{category}</h3>
                          <div className="nominees-list">
                            {groupedData[category].map((nominee, index) => (
                              <motion.div
                                key={nominee.id}
                                className={`nominee-result ${index === 0 ? 'winner' : ''}`}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                              >
                                <div className="position-badge">
                                  {index === 0 ? <FaTrophy /> :
                                   index === 1 ? <FaMedal /> :
                                   index === 2 ? <FaAward /> :
                                   `#${index + 1}`}
                                </div>
                                <img src={nominee.image_url} alt={nominee.name} className="nominee-image" />
                                <div className="nominee-info">
                                  <h4 className="nominee-name">{nominee.name}</h4>
                                  <p className="nominee-votes">{nominee.vote} votes</p>
                                  {index === 0 && <span className="winner-badge">🏆 Winner</span>}
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </motion.div>
                </>
              )}
            </>
          )}

          {/* No event selected message */}
          {!selectedEvent && events.length > 0 && (
            <div className="no-results">
              <h2>Select an Event</h2>
              <p>Please select an event from the dropdown above to view voting results.</p>
            </div>
          )}
        </motion.div>
      </div>
    </>
  );
};

export default ResultsPage;
