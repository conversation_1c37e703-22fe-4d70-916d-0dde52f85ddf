import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSearch, FaTrophy, FaUsers, FaMoneyBillWave, FaPlus, FaArrowRight } from 'react-icons/fa';
import { fetchEvents, selectEvents, selectEventsStatus, selectEventsError } from '../reudx/slice/EventsSlice';
import { selectIsAuthenticated } from '../reudx/slice/AuthSlice';
import LoadingSpinner from './LoadingSpinner';

const EventCard = ({ event, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -8, scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    className="event-card-wrapper"
  >
    <Link to={`/events/${event.id}`} className="event-card modern">
      <div className="event-card-header">
        <div className="event-image-wrapper">
          {event.image_url ? (
            <img className="event-image" src={event.image_url} alt={event.name} />
          ) : (
            <div className="event-image-placeholder">
              <FaTrophy size={40} color="#818cf8" />
            </div>
          )}
          <div className="event-overlay">
            <div className="overlay-content">
              <FaUsers size={20} color="white" />
              <span className="overlay-text">View Event</span>
            </div>
          </div>
        </div>
        <div className="event-status-badge">
          <span className="status-dot active"></span>
          <span className="status-text">Active</span>
        </div>
      </div>

      <div className="event-content">
        <div className="event-info">
          <h3 className="event-title">{event.name}</h3>
          <p className="event-description">{event.description}</p>
        </div>

        <div className="event-stats">
          <div className="stat-item">
            <div className="stat-icon">
              <FaMoneyBillWave />
            </div>
            <div className="stat-content">
              <span className="stat-label">Price per vote</span>
              <span className="stat-value">GH₵{event.voting_price}</span>
            </div>
          </div>

          {event.categories && (
            <div className="stat-item">
              <div className="stat-icon">
                <FaTrophy />
              </div>
              <div className="stat-content">
                <span className="stat-label">Categories</span>
                <span className="stat-value">{event.categories.length}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="event-card-footer">
        <div className="footer-content">
          <span className="action-text">Explore Categories</span>
          <div className="action-arrow">
            <FaArrowRight />
          </div>
        </div>
      </div>
    </Link>
  </motion.div>
);

const EventList = () => {
  const dispatch = useDispatch();
  const events = useSelector(selectEvents);
  const status = useSelector(selectEventsStatus);
  const error = useSelector(selectEventsError);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  const filteredEvents = events.filter((event) =>
    event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (status === 'loading') {
    return <LoadingSpinner message="Loading events..." type="events" />;
  }

  if (status === 'failed') {
    return (
      <div className="error-container">
        <h2>Error Loading Events</h2>
        <p>{error}</p>
        <button onClick={() => dispatch(fetchEvents())} className="btn btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <motion.div
            className="logo"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <FaTrophy size={24} color="#818cf8" />
            EaseVote
          </motion.div>
          <nav>
            <Link to="/results" className="btn btn-secondary btn-sm">
              View Results
            </Link>
          </nav>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="main-container">
        <motion.div
          className="events-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="events-title-container"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="title-section">
              <h1 className="events-main-title">Award Events</h1>
              <p className="events-subtitle">Discover and participate in exciting award ceremonies</p>
            </div>

            {/* Search and Filter Section */}
            <div className="events-controls">
              <motion.div
                className="search-container enhanced"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <FaSearch className="search-icon" />
                <input
                  className="search-bar"
                  type="text"
                  placeholder="Search events by name or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </motion.div>

              <div className="events-stats">
                <div className="stat-badge">
                  <FaTrophy className="stat-icon" />
                  <span>{filteredEvents.length} Events Available</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Events Grid */}
          <motion.div
            className="events-grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <AnimatePresence>
              {filteredEvents.map((event, index) => (
                <EventCard key={event.id} event={event} index={index} />
              ))}
            </AnimatePresence>
          </motion.div>

          {/* No Results */}
          <AnimatePresence>
            {filteredEvents.length === 0 && searchQuery && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                style={{ textAlign: 'center', padding: '2rem' }}
              >
                <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                  No events found matching "{searchQuery}"
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* No Events */}
          {filteredEvents.length === 0 && !searchQuery && status === 'succeeded' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              style={{ textAlign: 'center', padding: '2rem' }}
            >
              <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                No events available at the moment. Please check back later.
              </p>
            </motion.div>
          )}
        </motion.div>
      </main>
    </>
  );
};

export default EventList; 