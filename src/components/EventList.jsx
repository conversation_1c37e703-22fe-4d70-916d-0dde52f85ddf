import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSearch, FaTrophy, FaUsers, FaMoneyBillWave, FaPlus } from 'react-icons/fa';
import { fetchEvents, selectEvents, selectEventsStatus, selectEventsError } from '../reudx/slice/EventsSlice';
import { selectIsAuthenticated } from '../reudx/slice/AuthSlice';
import LoadingSpinner from './LoadingSpinner';

const EventCard = ({ event, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -8, scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
  >
    <Link to={`/events/${event.id}`} className="event-card">
      <div className="event-image-wrapper">
        {event.image_url ? (
          <img className="event-image" src={event.image_url} alt={event.name} />
        ) : (
          <div className="event-image-placeholder">
            <FaTrophy size={40} color="#818cf8" />
          </div>
        )}
        <div className="event-overlay">
          <FaUsers size={20} color="white" />
        </div>
      </div>
      <div className="event-info">
        <h3 className="event-title">{event.name}</h3>
        <p className="event-description">{event.description}</p>
        <div className="event-meta">
          <div className="voting-price">
            <FaMoneyBillWave size={16} color="#10b981" />
            <span>{event.voting_price} GHS per vote</span>
          </div>
          {event.categories && (
            <div className="category-count">
              <FaTrophy size={16} color="#f59e0b" />
              <span>{event.categories.length} categories</span>
            </div>
          )}
        </div>
      </div>
      <div className="card-footer">
        <span className="vote-text">View Categories</span>
        <div className="arrow-icon">→</div>
      </div>
    </Link>
  </motion.div>
);

const EventList = () => {
  const dispatch = useDispatch();
  const events = useSelector(selectEvents);
  const status = useSelector(selectEventsStatus);
  const error = useSelector(selectEventsError);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  const filteredEvents = events.filter((event) =>
    event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (status === 'loading') {
    return <LoadingSpinner message="Loading events..." type="events" />;
  }

  if (status === 'failed') {
    return (
      <div className="error-container">
        <h2>Error Loading Events</h2>
        <p>{error}</p>
        <button onClick={() => dispatch(fetchEvents())} className="btn btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="app-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <motion.div
            className="logo"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <FaTrophy size={24} color="#818cf8" />
            EaseVote
          </motion.div>
          <nav>
            <Link to="/results" className="btn btn-secondary btn-sm">
              View Results
            </Link>
            {isAuthenticated && (
              <Link to="/admin/create-event" className="btn btn-primary btn-sm">
                <FaPlus size={14} />
                Create Event
              </Link>
            )}
          </nav>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="main-container">
        <motion.div
          className="events-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="events-title-container"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h1>Voting Events</h1>
            <h2>Choose an event to view categories and cast your votes</h2>

            {/* Search Bar */}
            <motion.div
              className="search-container"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <FaSearch className="search-icon" />
              <input
                className="search-bar"
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </motion.div>
          </motion.div>

          {/* Events Grid */}
          <motion.div
            className="events-grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <AnimatePresence>
              {filteredEvents.map((event, index) => (
                <EventCard key={event.id} event={event} index={index} />
              ))}
            </AnimatePresence>
          </motion.div>

          {/* No Results */}
          <AnimatePresence>
            {filteredEvents.length === 0 && searchQuery && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                style={{ textAlign: 'center', padding: '2rem' }}
              >
                <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                  No events found matching "{searchQuery}"
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* No Events */}
          {filteredEvents.length === 0 && !searchQuery && status === 'succeeded' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              style={{ textAlign: 'center', padding: '2rem' }}
            >
              <p style={{ color: 'var(--text-muted)', fontSize: '1.1rem' }}>
                No events available at the moment. Please check back later.
              </p>
              {isAuthenticated && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  style={{ marginTop: '1rem' }}
                >
                  <Link to="/admin/create-event" className="btn btn-primary">
                    <FaPlus size={16} />
                    Create Your First Event
                  </Link>
                </motion.div>
              )}
            </motion.div>
          )}
        </motion.div>
      </main>
    </>
  );
};

export default EventList; 