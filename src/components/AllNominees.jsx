import { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { fetchNominees, deleteNominee, createNominee } from '../reudx/slice/NomineesSlice';
import categories from './helpers/Categories';

const AllNominees = () => {
  const dispatch = useDispatch();
  const nominees = useSelector((state) => state.nominees.nominees);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [filteredCategories, setFilteredCategories] = useState([]);
  const fileInputRef = useRef(null);

  const [newNomineeData, setNomineeData] = useState({
    name: '',
    image: null,
    category: '',
  });

  useEffect(() => {
    dispatch(fetchNominees());
  }, [dispatch]);

  const showPaymentSuccessToast = () => {
    toast.success('Nominee added successfully', {
      position: toast.POSITION.BOTTOM_RIGHT,
      autoClose: 4000,
    });
  };

  const handleSearch = (query) => {
    const filtered = nominees
      .filter((nominee) => nominee.category.toLowerCase().includes(query.toLowerCase()))
      .sort((a, b) => b.vote - a.vote);
    setFilteredCategories(filtered);
  };

  const handleDelete = async (id) => {
    try {
      dispatch(deleteNominee(id));
    } catch (err) {
      throw new Error('Failed to delete nominee', err);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, files } = e.target;
    if (name === 'image') {
      setNomineeData({ ...newNomineeData, image: files[0] });
    } else {
      setNomineeData({ ...newNomineeData, [name]: value });
    }
  };

  const handleAddNominee = () => {
    setLoading(true);
    const formData = new FormData();
    formData.append('nominee[name]', newNomineeData.name);
    formData.append('nominee[image]', newNomineeData.image);
    formData.append('nominee[category]', newNomineeData.category);

    dispatch(createNominee(formData))
      .then(() => {
        setNomineeData({
          name: '',
          image: null,
          category: '',
        });
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        showPaymentSuccessToast();
      })
      .catch((error) => {
        toast.error('Failed to create nominee:', error);
      })
      .finally(() => {
        setLoading(false); // Reset loading state
      });
  };

  return (
    <>
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <Link to="/" className="logo">🏆 Award Voting System</Link>
          <nav>
            <Link to="/" className="btn btn-secondary btn-sm">
              ← Back to Categories
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <div className="main-container">
        <div className="home-title-container">
          <h1>All Nominees</h1>
          <h2>Manage nominees and view voting results</h2>
        </div>

        {/* Search Bar */}
        <div className="search-container">
          <div className="search-icon">🔍</div>
          <input
            className="search-bar"
            type="text"
            placeholder="Search nominees by category..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              handleSearch(e.target.value);
            }}
          />
        </div>
        {/* Nominees List */}
        <div className="nominees-grid">
          {filteredCategories.map((nominee) => (
            <div key={nominee.id} className={`nominee-card ${nominee.vote === filteredCategories[0]?.vote ? 'winner' : ''}`}>
              <img src={nominee.image_url} alt={nominee.name} className="nominee-image" />
              <div className="nominee-info">
                <h3 className="nominee-name">{nominee.name}</h3>
                <p className="nominee-code">Category: {nominee.category}</p>
                <p className="nominee-code">Votes: {nominee.vote}</p>
              </div>
              <button
                type="button"
                onClick={() => handleDelete(nominee.id)}
                className="btn btn-danger btn-sm"
                disabled
              >
                🗑️ Delete
              </button>
            </div>
          ))}
        </div>

        {/* Add Nominee Form */}
        <div className="nominee-card" style={{ maxWidth: '500px', margin: '2rem auto' }}>
          <h2 style={{ textAlign: 'center', marginBottom: '1.5rem', color: 'var(--text-primary)' }}>
            ➕ Add New Nominee
          </h2>

          <div className="form-group">
            <label className="form-label">Nominee Name</label>
            <input
              type="text"
              name="name"
              placeholder="Enter nominee name"
              value={newNomineeData.name}
              onChange={handleInputChange}
              className="form-input"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Nominee Image</label>
            <input
              type="file"
              name="image"
              ref={fileInputRef}
              onChange={handleInputChange}
              className="form-input"
              accept="image/*"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Category</label>
            <select
              name="category"
              value={newNomineeData.category}
              onChange={handleInputChange}
              required
              className="form-select"
            >
              <option value="" disabled>Select Nominee Category</option>
              {categories.map((category) => (
                <option key={category.title} value={category.title}>
                  {category.title}
                </option>
              ))}
            </select>
          </div>

          <button
            type="button"
            onClick={handleAddNominee}
            disabled={loading}
            className={`btn btn-success btn-lg ${loading ? 'loading' : ''}`}
            style={{ width: '100%' }}
          >
            {loading ? '⏳ Adding...' : '✅ Add Nominee'}
          </button>
        </div>
      </div>
    </>
  );
};

export default AllNominees;
