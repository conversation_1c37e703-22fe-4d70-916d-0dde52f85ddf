import { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { fetchNominees, deleteNominee, createNominee } from '../reudx/slice/NomineesSlice';

const AllNominees = () => {
  const dispatch = useDispatch();
  const nominees = useSelector((state) => state.nominees.nominees);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [filteredCategories, setFilteredCategories] = useState([]);
  const fileInputRef = useRef(null);

  const [newNomineeData, setNomineeData] = useState({
    name: '',
    image: null,
    category: '',
  });

  useEffect(() => {
    dispatch(fetchNominees());
  }, [dispatch]);

  const showPaymentSuccessToast = () => {
    toast.success('Nominee added successfully', {
      position: toast.POSITION.BOTTOM_RIGHT,
      autoClose: 4000,
    });
  };

  const handleSearch = (query) => {
    const filtered = nominees
      .filter((nominee) => nominee.category.toLowerCase().includes(query.toLowerCase()))
      .sort((a, b) => b.vote - a.vote);
    setFilteredCategories(filtered);
  };

  const handleDelete = async (id) => {
    try {
      dispatch(deleteNominee(id));
    } catch (err) {
      throw new Error('Failed to delete nominee', err);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, files } = e.target;
    if (name === 'image') {
      setNomineeData({ ...newNomineeData, image: files[0] });
    } else {
      setNomineeData({ ...newNomineeData, [name]: value });
    }
  };

  const handleAddNominee = () => {
    setLoading(true);
    const formData = new FormData();
    formData.append('nominee[name]', newNomineeData.name);
    formData.append('nominee[image]', newNomineeData.image);
    formData.append('nominee[category]', newNomineeData.category);

    dispatch(createNominee(formData))
      .then(() => {
        setNomineeData({
          name: '',
          image: null,
          category: '',
        });
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        showPaymentSuccessToast();
      })
      .catch((error) => {
        toast.error('Failed to create nominee:', error);
      })
      .finally(() => {
        setLoading(false); // Reset loading state
      });
  };

  return (
    <div className="over-main">
      <input
        className="search-bar"
        type="text"
        placeholder="Search by categories"
        value={searchQuery}
        onChange={(e) => {
          setSearchQuery(e.target.value);
          handleSearch(e.target.value);
        }}
      />
      <div className="all-main-container">
        <div className="all-container">
          {filteredCategories.map((nominee) => (
            <div key={nominee.id} className={nominee.vote === filteredCategories[0].vote ? 'winner' : ''}>
              <img src={nominee.image_url} alt={nominee.name} />
              <h3>
                Name:
                {nominee.name}
              </h3>
              <h3>
                Category:
                {nominee.category}
              </h3>
              <h3>
                Votes:
                {nominee.vote}
              </h3>
              <button disabled type="button" onClick={() => handleDelete(nominee.id)}>Delete</button>
            </div>
          ))}
        </div>
        <div className="add-data">
          <h2>Add New Nominee</h2>
          <input
            type="text"
            name="name"
            placeholder="Nominee Name"
            value={newNomineeData.name}
            onChange={handleInputChange}
          />
          <input
            type="file"
            name="image"
            placeholder="Nominee Image"
            ref={fileInputRef}
            onChange={handleInputChange}
          />
          <input
            type="text"
            name="category"
            placeholder="Nominee Category"
            value={newNomineeData.category}
            onChange={handleInputChange}
          />
          <button
            type="button"
            onClick={handleAddNominee}
            disabled={loading}
            className={loading ? 'loading-button' : ''}
          >
            {loading ? 'Loading...' : 'Add Nominee'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AllNominees;
