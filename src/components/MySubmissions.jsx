import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaCalendarAlt,
  FaTrophy,
  FaUsers,
  FaEdit,
  FaTrash,
  FaPlus,
  FaEye,
  FaChartBar,
  FaMoneyBillWave,
  FaVoteYea,
  FaSearch,
  FaFilter
} from 'react-icons/fa';
import { fetchEvents, deleteEvent } from '../reudx/slice/EventsSlice';
import { fetchCategories, deleteCategory } from '../reudx/slice/CategoriesSlice';
import { deleteNominee } from '../reudx/slice/NomineesSlice';
import EventForm from './admin/EventForm';
import CategoryForm from './admin/CategoryForm';
import NomineeForm from './admin/NomineeForm';
import LoadingSpinner from './LoadingSpinner';

// Event Card Component
const EventCard = ({ event, index, onEdit, onDelete }) => (
  <motion.div
    className="submission-card event-card"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -5, scale: 1.02 }}
  >
    <div className="card-header">
      <div className="card-icon">
        <FaCalendarAlt />
      </div>
      <div className="card-status">
        <span className="status-dot active"></span>
        <span className="status-text">Active</span>
      </div>
    </div>

    <div className="card-content">
      <h3 className="card-title">{event.name}</h3>
      <p className="card-description">{event.description}</p>

      <div className="card-stats">
        <div className="stat-item">
          <FaMoneyBillWave className="stat-icon" />
          <span>GH₵{event.voting_price}/vote</span>
        </div>
        <div className="stat-item">
          <FaVoteYea className="stat-icon" />
          <span>0 total votes</span>
        </div>
      </div>
    </div>

    <div className="card-actions">
      <Link to={`/events/${event.id}`} className="btn btn-sm btn-secondary">
        <FaEye /> View
      </Link>
      <button onClick={onEdit} className="btn btn-sm btn-primary">
        <FaEdit /> Edit
      </button>
      <button onClick={onDelete} className="btn btn-sm btn-danger">
        <FaTrash /> Delete
      </button>
    </div>
  </motion.div>
);

// Category Card Component
const CategoryCard = ({ category, index, onEdit, onDelete }) => (
  <motion.div
    className="submission-card category-card"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -5, scale: 1.02 }}
  >
    <div className="card-header">
      <div className="card-icon">
        <FaTrophy />
      </div>
      <div className="card-status">
        <span className="status-dot active"></span>
        <span className="status-text">Active</span>
      </div>
    </div>

    <div className="card-content">
      <h3 className="card-title">{category.name}</h3>
      <p className="card-description">{category.description || "Category description"}</p>

      <div className="card-stats">
        <div className="stat-item">
          <FaUsers className="stat-icon" />
          <span>0 nominees</span>
        </div>
        <div className="stat-item">
          <FaVoteYea className="stat-icon" />
          <span>0 votes</span>
        </div>
      </div>
    </div>

    <div className="card-actions">
      <button onClick={onEdit} className="btn btn-sm btn-primary">
        <FaEdit /> Edit
      </button>
      <button onClick={onDelete} className="btn btn-sm btn-danger">
        <FaTrash /> Delete
      </button>
    </div>
  </motion.div>
);

// Nominee Card Component
const NomineeSubmissionCard = ({ nominee, index, onEdit, onDelete }) => (
  <motion.div
    className="submission-card nominee-card"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    whileHover={{ y: -5, scale: 1.02 }}
  >
    <div className="card-header">
      <div className="nominee-image">
        {nominee.image_url ? (
          <img src={nominee.image_url} alt={nominee.name} />
        ) : (
          <div className="image-placeholder">
            <FaUsers />
          </div>
        )}
      </div>
      <div className="card-status">
        <span className="status-dot active"></span>
        <span className="status-text">Active</span>
      </div>
    </div>

    <div className="card-content">
      <h3 className="card-title">{nominee.name}</h3>
      <p className="card-description">Code: {nominee.nominee_code}</p>

      <div className="card-stats">
        <div className="stat-item">
          <FaVoteYea className="stat-icon" />
          <span>{nominee.vote || 0} votes</span>
        </div>
        <div className="stat-item">
          <FaTrophy className="stat-icon" />
          <span>{nominee.category}</span>
        </div>
      </div>
    </div>

    <div className="card-actions">
      <button onClick={onEdit} className="btn btn-sm btn-primary">
        <FaEdit /> Edit
      </button>
      <button onClick={onDelete} className="btn btn-sm btn-danger">
        <FaTrash /> Delete
      </button>
    </div>
  </motion.div>
);

const MySubmissions = () => {
  const dispatch = useDispatch();
  const { user } = useSelector(state => state.auth);
  const { events, status: eventsStatus } = useSelector(state => state.events);
  const { categories } = useSelector(state => state.categories);
  const { nominees } = useSelector(state => state.nominees);

  // State management
  const [modal, setModal] = useState({ type: null, data: null });
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [activeTab, setActiveTab] = useState('events');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');

  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  const myEvents = events.filter(e => e.user_id === user?.data?.id);

  useEffect(() => {
    if (eventsStatus !== 'succeeded' || myEvents.length === 0) return;

    // Fetch categories for each event the user owns with a delay to prevent resource exhaustion
    const fetchCategoriesSequentially = async () => {
      setIsLoadingCategories(true);
      try {
        for (const event of myEvents) {
          try {
            await dispatch(fetchCategories(event.id));
            // Small delay between requests to prevent overwhelming the server
            await new Promise(resolve => setTimeout(resolve, 100));
          } catch (error) {
            console.error(`Failed to fetch categories for event ${event.id}:`, error);
          }
        }
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategoriesSequentially();
  }, [dispatch, eventsStatus]); // Removed myEvents from dependencies to prevent infinite loops

  const myCategories = categories.filter(c => c.user_id === user?.data?.id);
  const myNominees = nominees.filter(n => n.user_id === user?.data?.id);

  // Edit/Delete handlers
  const handleEdit = (type, data) => setModal({ type: `edit-${type}`, data });
  const handleDelete = (type, data) => setModal({ type: `delete-${type}`, data });
  const closeModal = () => setModal({ type: null, data: null });

  // Confirm delete for event
  const confirmDeleteEvent = async (eventId) => {
    try {
      await dispatch(deleteEvent(eventId)).unwrap();
      closeModal();
      dispatch(fetchEvents());
    } catch (err) {
      alert('Failed to delete event.');
    }
  };

  // Confirm delete for category
  const confirmDeleteCategory = async (eventId, categoryId) => {
    try {
      await dispatch(deleteCategory({ eventId, categoryId })).unwrap();
      closeModal();
      dispatch(fetchCategories());
    } catch (err) {
      alert('Failed to delete category.');
    }
  };

  // Confirm delete for nominee
  const confirmDeleteNominee = async (eventId, categoryId, nomineeId) => {
    try {
      await dispatch(deleteNominee({ eventId, categoryId, nomineeId })).unwrap();
      closeModal();
      // Optionally refresh nominees
    } catch (err) {
      alert('Failed to delete nominee.');
    }
  };

  // Filter functions
  const filteredEvents = myEvents.filter(event =>
    event.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredCategories = myCategories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredNominees = myNominees.filter(nominee =>
    nominee.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoadingCategories) {
    return <LoadingSpinner message="Loading your submissions..." type="pulse" />;
  }

  return (
    <>
      {/* Header */}
      <motion.header
        className="submissions-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <div className="submissions-title">
            <h1>My Submissions</h1>
            <p>Manage your events, categories, and nominees</p>
          </div>
          <div className="header-actions">
            <Link to="/create-event" className="btn btn-primary">
              <FaPlus /> Create Event
            </Link>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <div className="submissions-container">
        {/* Stats Overview */}
        <motion.div
          className="submissions-stats"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="stats-grid">
            <div className="stat-card events">
              <div className="stat-icon">
                <FaCalendarAlt />
              </div>
              <div className="stat-content">
                <h3>Events</h3>
                <p className="stat-number">{myEvents.length}</p>
              </div>
            </div>

            <div className="stat-card categories">
              <div className="stat-icon">
                <FaTrophy />
              </div>
              <div className="stat-content">
                <h3>Categories</h3>
                <p className="stat-number">{myCategories.length}</p>
              </div>
            </div>

            <div className="stat-card nominees">
              <div className="stat-icon">
                <FaUsers />
              </div>
              <div className="stat-content">
                <h3>Nominees</h3>
                <p className="stat-number">{myNominees.length}</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter */}
        <motion.div
          className="submissions-controls"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="search-container">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search your submissions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="tab-navigation">
            <button
              onClick={() => setActiveTab('events')}
              className={`tab-btn ${activeTab === 'events' ? 'active' : ''}`}
            >
              <FaCalendarAlt /> Events ({filteredEvents.length})
            </button>
            <button
              onClick={() => setActiveTab('categories')}
              className={`tab-btn ${activeTab === 'categories' ? 'active' : ''}`}
            >
              <FaTrophy /> Categories ({filteredCategories.length})
            </button>
            <button
              onClick={() => setActiveTab('nominees')}
              className={`tab-btn ${activeTab === 'nominees' ? 'active' : ''}`}
            >
              <FaUsers /> Nominees ({filteredNominees.length})
            </button>
          </div>
        </motion.div>

        {/* Content Sections */}
        <motion.div
          className="submissions-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <AnimatePresence mode="wait">
            {activeTab === 'events' && (
              <motion.div
                key="events"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="submissions-section"
              >
                <div className="section-header">
                  <h2>My Events</h2>
                  <Link to="/create-event" className="btn btn-secondary btn-sm">
                    <FaPlus /> Add Event
                  </Link>
                </div>

                {filteredEvents.length === 0 ? (
                  <div className="empty-state">
                    <FaCalendarAlt className="empty-icon" />
                    <h3>No events found</h3>
                    <p>You haven't created any events yet or no events match your search.</p>
                    <Link to="/create-event" className="btn btn-primary">
                      <FaPlus /> Create Your First Event
                    </Link>
                  </div>
                ) : (
                  <div className="submissions-grid">
                    {filteredEvents.map((event, index) => (
                      <EventCard
                        key={event.id}
                        event={event}
                        index={index}
                        onEdit={() => handleEdit('event', event)}
                        onDelete={() => handleDelete('event', event)}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}

            {activeTab === 'categories' && (
              <motion.div
                key="categories"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="submissions-section"
              >
                <div className="section-header">
                  <h2>My Categories</h2>
                </div>

                {filteredCategories.length === 0 ? (
                  <div className="empty-state">
                    <FaTrophy className="empty-icon" />
                    <h3>No categories found</h3>
                    <p>You haven't created any categories yet or no categories match your search.</p>
                  </div>
                ) : (
                  <div className="submissions-grid">
                    {filteredCategories.map((category, index) => (
                      <CategoryCard
                        key={category.id}
                        category={category}
                        index={index}
                        onEdit={() => handleEdit('category', category)}
                        onDelete={() => handleDelete('category', category)}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}

            {activeTab === 'nominees' && (
              <motion.div
                key="nominees"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="submissions-section"
              >
                <div className="section-header">
                  <h2>My Nominees</h2>
                </div>

                {filteredNominees.length === 0 ? (
                  <div className="empty-state">
                    <FaUsers className="empty-icon" />
                    <h3>No nominees found</h3>
                    <p>You haven't created any nominees yet or no nominees match your search.</p>
                  </div>
                ) : (
                  <div className="submissions-grid">
                    {filteredNominees.map((nominee, index) => (
                      <NomineeSubmissionCard
                        key={nominee.id}
                        nominee={nominee}
                        index={index}
                        onEdit={() => handleEdit('nominee', nominee)}
                        onDelete={() => handleDelete('nominee', nominee)}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Modals for edit/delete */}
      {modal.type === 'edit-event' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Edit Event</h2>
            <EventForm eventData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchEvents()); }} />
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'delete-event' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Delete Event</h2>
            <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
            <button onClick={() => confirmDeleteEvent(modal.data.id)} className="btn btn-danger">Delete</button>
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'edit-category' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Edit Category</h2>
            <CategoryForm eventId={modal.data.event_id} categoryData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchCategories()); }} />
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'delete-category' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Delete Category</h2>
            <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
            <button onClick={() => confirmDeleteCategory(modal.data.event_id, modal.data.id)} className="btn btn-danger">Delete</button>
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'edit-nominee' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Edit Nominee</h2>
            <NomineeForm eventId={modal.data.event_id} categoryId={modal.data.category_id} nomineeData={modal.data} onSuccess={() => { closeModal(); /* Optionally refresh nominees */ }} />
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'delete-nominee' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Delete Nominee</h2>
            <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
            <button onClick={() => confirmDeleteNominee(modal.data.event_id, modal.data.category_id, modal.data.id)} className="btn btn-danger">Delete</button>
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      </>
    );
  };

export default MySubmissions; 