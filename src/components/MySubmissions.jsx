import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchEvents, deleteEvent } from '../reudx/slice/EventsSlice';
import { fetchCategories, deleteCategory } from '../reudx/slice/CategoriesSlice';
import { deleteNominee } from '../reudx/slice/NomineesSlice';
import EventForm from './admin/EventForm';
import CategoryForm from './admin/CategoryForm';
import NomineeForm from './admin/NomineeForm';

const MySubmissions = () => {
  const dispatch = useDispatch();
  const { user } = useSelector(state => state.auth);
  const { events, status: eventsStatus } = useSelector(state => state.events);
  const { categories } = useSelector(state => state.categories);
  const { nominees } = useSelector(state => state.nominees);

  // Modal state
  const [modal, setModal] = useState({ type: null, data: null });

  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  const myEvents = events.filter(e => e.user_id === user?.data?.id);

  useEffect(() => {
    if (eventsStatus !== 'succeeded') return;
    // Fetch categories for each event the user owns
    myEvents.forEach(event => {
      dispatch(fetchCategories(event.id));
    });
  }, [dispatch, myEvents, eventsStatus]);

  const myCategories = categories.filter(c => c.user_id === user?.data?.id);
  const myNominees = nominees.filter(n => n.user_id === user?.data?.id);

  // Edit/Delete handlers
  const handleEdit = (type, data) => setModal({ type: `edit-${type}`, data });
  const handleDelete = (type, data) => setModal({ type: `delete-${type}`, data });
  const closeModal = () => setModal({ type: null, data: null });

  // Confirm delete for event
  const confirmDeleteEvent = async (eventId) => {
    try {
      await dispatch(deleteEvent(eventId)).unwrap();
      closeModal();
      dispatch(fetchEvents());
    } catch (err) {
      alert('Failed to delete event.');
    }
  };

  // Confirm delete for category
  const confirmDeleteCategory = async (eventId, categoryId) => {
    try {
      await dispatch(deleteCategory({ eventId, categoryId })).unwrap();
      closeModal();
      dispatch(fetchCategories());
    } catch (err) {
      alert('Failed to delete category.');
    }
  };

  // Confirm delete for nominee
  const confirmDeleteNominee = async (eventId, categoryId, nomineeId) => {
    try {
      await dispatch(deleteNominee({ eventId, categoryId, nomineeId })).unwrap();
      closeModal();
      // Optionally refresh nominees
    } catch (err) {
      alert('Failed to delete nominee.');
    }
  };

  return (
    <div className="my-submissions-container">
      <h2>My Submissions</h2>
      <section>
        <h3>My Events</h3>
        {myEvents.length === 0 ? <p>You haven't created any events yet.</p> : (
          <ul>
            {myEvents.map(event => (
              <li key={event.id}>
                <strong>{event.name}</strong>
                <button onClick={() => handleEdit('event', event)}>Edit</button>
                <button onClick={() => handleDelete('event', event)}>Delete</button>
              </li>
            ))}
          </ul>
        )}
      </section>
      <section>
        <h3>My Categories</h3>
        {myCategories.length === 0 ? <p>You haven't created any categories yet.</p> : (
          <ul>
            {myCategories.map(category => (
              <li key={category.id}>
                <strong>{category.name}</strong>
                <button onClick={() => handleEdit('category', category)}>Edit</button>
                <button onClick={() => handleDelete('category', category)}>Delete</button>
              </li>
            ))}
          </ul>
        )}
      </section>
      <section>
        <h3>My Nominees</h3>
        {myNominees.length === 0 ? <p>You haven't created any nominees yet.</p> : (
          <ul>
            {myNominees.map(nominee => (
              <li key={nominee.id}>
                <strong>{nominee.name}</strong>
                <button onClick={() => handleEdit('nominee', nominee)}>Edit</button>
                <button onClick={() => handleDelete('nominee', nominee)}>Delete</button>
              </li>
            ))}
          </ul>
        )}
      </section>

      {/* Modals for edit/delete */}
      {modal.type === 'edit-event' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Edit Event</h2>
            <EventForm eventData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchEvents()); }} />
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'delete-event' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Delete Event</h2>
            <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
            <button onClick={() => confirmDeleteEvent(modal.data.id)} className="btn btn-danger">Delete</button>
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'edit-category' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Edit Category</h2>
            <CategoryForm eventId={modal.data.event_id} categoryData={modal.data} onSuccess={() => { closeModal(); dispatch(fetchCategories()); }} />
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'delete-category' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Delete Category</h2>
            <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
            <button onClick={() => confirmDeleteCategory(modal.data.event_id, modal.data.id)} className="btn btn-danger">Delete</button>
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'edit-nominee' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Edit Nominee</h2>
            <NomineeForm eventId={modal.data.event_id} categoryId={modal.data.category_id} nomineeData={modal.data} onSuccess={() => { closeModal(); /* Optionally refresh nominees */ }} />
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
      {modal.type === 'delete-nominee' && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>Delete Nominee</h2>
            <p>Are you sure you want to delete <b>{modal.data.name}</b>?</p>
            <button onClick={() => confirmDeleteNominee(modal.data.event_id, modal.data.category_id, modal.data.id)} className="btn btn-danger">Delete</button>
            <button onClick={closeModal} className="btn btn-secondary">Cancel</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MySubmissions; 