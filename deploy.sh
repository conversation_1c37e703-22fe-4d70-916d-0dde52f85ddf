#!/bin/bash

# Navigate to the project directory
cd "$(dirname "$0")"

# Pull the latest changes from GitHub
echo "Pulling the latest changes from GitHub..."
git pull origin development

# Install dependencies
echo "Installing dependencies..."
npm install
# OR, if using yarn:
# yarn install

# Build the Vite project
echo "Building the Vite project..."
npm run build
# OR, if using yarn:
# yarn build

# Restart the server (example using pm2)
echo "Restarting the server..."
sudo systemctl restart nginx
# OR, if using systemd:
# sudo systemctl restart your_service_name

echo "Deployment complete!"
