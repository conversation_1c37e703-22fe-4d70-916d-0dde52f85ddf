#!/bin/bash
set -e  # Exit on any error

# Navigate to the project directory
cd "$(dirname "$0")"

# Pull the latest changes from GitHub
echo "Pulling the latest changes from GitHub..."
git pull origin development

# Check if package.json or package-lock.json changed
if git diff HEAD~1 HEAD --name-only | grep -E "(package\.json|package-lock\.json)" > /dev/null; then
    echo "Dependencies changed, installing..."
    npm install --force
elif [ ! -d "node_modules" ]; then
    echo "node_modules not found, installing dependencies..."
    npm install --force
else
    echo "Dependencies up to date, skipping npm install"
fi

# Build the Vite project
echo "Building the Vite project..."
npm run build

# Restart the server (example using pm2)
echo "Restarting the server..."
sudo systemctl reload nginx

echo "Deployment complete!"
